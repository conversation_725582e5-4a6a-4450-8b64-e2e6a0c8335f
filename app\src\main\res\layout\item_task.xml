<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/card_task"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Task Title -->
        <TextView
            android:id="@+id/tv_task_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tv_task_priority"
            tools:text="Hoàn thành báo cáo tháng" />

        <!-- Priority Badge -->
        <TextView
            android:id="@+id/tv_task_priority"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textSize="10sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:background="@drawable/bg_priority_medium"
            android:paddingHorizontal="8dp"
            android:paddingVertical="4dp"
            app:layout_constraintTop_toTopOf="@+id/tv_task_title"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="Cao" />

        <!-- Task Description -->
        <TextView
            android:id="@+id/tv_task_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textSize="14sp"
            android:textColor="@color/text_secondary"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintTop_toBottomOf="@+id/tv_task_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="Cần hoàn thành báo cáo tháng 12 và gửi cho quản lý" />

        <!-- Progress Section -->
        <TextView
            android:id="@+id/tv_progress_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="Tiến độ:"
            android:textSize="12sp"
            android:textColor="@color/text_secondary"
            app:layout_constraintTop_toBottomOf="@+id/tv_task_description"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/tv_task_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="@color/primary"
            app:layout_constraintTop_toTopOf="@+id/tv_progress_label"
            app:layout_constraintStart_toEndOf="@+id/tv_progress_label"
            tools:text="75%" />

        <ProgressBar
            android:id="@+id/progress_bar_task"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="0dp"
            android:layout_height="6dp"
            android:layout_marginTop="4dp"
            android:progressTint="@color/primary"
            android:progressBackgroundTint="@color/gray_200"
            android:max="100"
            app:layout_constraintTop_toBottomOf="@+id/tv_progress_label"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:progress="75" />

        <!-- Due Date -->
        <TextView
            android:id="@+id/tv_task_due_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textSize="12sp"
            android:textColor="@color/text_hint"
            android:drawableStart="@drawable/ic_calendar_small"
            android:drawablePadding="4dp"
            app:layout_constraintTop_toBottomOf="@+id/progress_bar_task"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="Hết hạn: 31/12/2024" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
