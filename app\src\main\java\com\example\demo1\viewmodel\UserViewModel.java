package com.example.demo1.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.example.demo1.models.User;
import com.example.demo1.repository.TodoRepository;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * ViewModel cho User - Quản lý UI state và business logic liên quan đến User
 * Chứa các chức năng đăng ký, đăng nhập, quản lý thông tin user
 */
public class UserViewModel extends AndroidViewModel {
    
    private static final String TAG = "UserViewModel";
    
    private TodoRepository repository;
    
    // LiveData cho danh sách user
    private LiveData<List<User>> allActiveUsers;
    
    // LiveData cho user hiện tại
    private MutableLiveData<User> currentUser;
    
    // LiveData cho trạng thái loading
    private MutableLiveData<Boolean> isLoading;
    
    // LiveData cho thông báo lỗi
    private MutableLiveData<String> errorMessage;
    
    // LiveData cho trạng thái đăng nhập
    private MutableLiveData<Boolean> isLoggedIn;
    
    /**
     * Constructor khởi tạo ViewModel
     * @param application Application context
     */
    public UserViewModel(@NonNull Application application) {
        super(application);
        Log.d(TAG, "UserViewModel: Khởi tạo UserViewModel");
        
        // Khởi tạo repository
        repository = new TodoRepository(application);
        
        // Khởi tạo LiveData
        allActiveUsers = repository.getAllActiveUsers();
        currentUser = new MutableLiveData<>();
        isLoading = new MutableLiveData<>(false);
        errorMessage = new MutableLiveData<>();
        isLoggedIn = new MutableLiveData<>(false);
        
        Log.d(TAG, "UserViewModel: Khởi tạo thành công tất cả LiveData");
    }
    
    // ==================== GETTER METHODS ====================
    
    public LiveData<List<User>> getAllActiveUsers() {
        Log.d(TAG, "getAllActiveUsers: Lấy danh sách tất cả user đang hoạt động");
        return allActiveUsers;
    }
    
    public LiveData<User> getCurrentUser() {
        Log.d(TAG, "getCurrentUser: Lấy thông tin user hiện tại");
        return currentUser;
    }
    
    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }
    
    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }
    
    public LiveData<Boolean> getIsLoggedIn() {
        return isLoggedIn;
    }
    
    // ==================== USER REGISTRATION ====================
    
    /**
     * Đăng ký user mới
     * @param username Tên đăng nhập
     * @param email Email
     * @param fullName Họ tên đầy đủ
     */
    public void registerUser(String username, String email, String fullName) {
        Log.d(TAG, "registerUser: Bắt đầu đăng ký user - username: " + username + ", email: " + email);
        
        // Validate input
        if (!validateRegistrationInput(username, email, fullName)) {
            Log.e(TAG, "registerUser: Validation failed");
            return;
        }
        
        isLoading.setValue(true);
        
        // Kiểm tra username đã tồn tại chưa
        try {
            boolean usernameExists = repository.checkUsernameExists(username).get();
            Log.d(TAG, "registerUser: Kiểm tra username tồn tại - kết quả: " + usernameExists);
            
            if (usernameExists) {
                errorMessage.setValue("Tên đăng nhập đã tồn tại");
                isLoading.setValue(false);
                Log.w(TAG, "registerUser: Username đã tồn tại");
                return;
            }
            
            // Kiểm tra email đã tồn tại chưa
            boolean emailExists = repository.checkEmailExists(email).get();
            Log.d(TAG, "registerUser: Kiểm tra email tồn tại - kết quả: " + emailExists);
            
            if (emailExists) {
                errorMessage.setValue("Email đã được sử dụng");
                isLoading.setValue(false);
                Log.w(TAG, "registerUser: Email đã tồn tại");
                return;
            }
            
            // Tạo user mới
            User newUser = new User(username, email, fullName);
            Log.d(TAG, "registerUser: Tạo user mới - " + newUser.toString());
            
            long userId = repository.insertUser(newUser).get();
            Log.d(TAG, "registerUser: Thêm user thành công - userId: " + userId);
            
            // Cập nhật user hiện tại
            newUser.setUserId((int) userId);
            currentUser.setValue(newUser);
            isLoggedIn.setValue(true);
            
            errorMessage.setValue("Đăng ký thành công!");
            Log.i(TAG, "registerUser: Đăng ký thành công cho user: " + username);
            
        } catch (ExecutionException | InterruptedException e) {
            Log.e(TAG, "registerUser: Lỗi khi đăng ký user", e);
            errorMessage.setValue("Có lỗi xảy ra khi đăng ký: " + e.getMessage());
        } finally {
            isLoading.setValue(false);
        }
    }
    
    // ==================== USER LOGIN ====================
    
    /**
     * Đăng nhập user
     * @param username Tên đăng nhập
     */
    public void loginUser(String username) {
        Log.d(TAG, "loginUser: Bắt đầu đăng nhập - username: " + username);
        
        if (username == null || username.trim().isEmpty()) {
            errorMessage.setValue("Vui lòng nhập tên đăng nhập");
            Log.w(TAG, "loginUser: Username trống");
            return;
        }
        
        isLoading.setValue(true);
        
        try {
            User user = repository.getUserByUsername(username.trim()).get();
            Log.d(TAG, "loginUser: Tìm kiếm user - kết quả: " + (user != null ? user.toString() : "null"));
            
            if (user != null && user.isActive()) {
                currentUser.setValue(user);
                isLoggedIn.setValue(true);
                errorMessage.setValue("Đăng nhập thành công!");
                Log.i(TAG, "loginUser: Đăng nhập thành công cho user: " + username);
            } else {
                errorMessage.setValue("Tên đăng nhập không tồn tại hoặc tài khoản đã bị vô hiệu hóa");
                Log.w(TAG, "loginUser: User không tồn tại hoặc không hoạt động");
            }
            
        } catch (ExecutionException | InterruptedException e) {
            Log.e(TAG, "loginUser: Lỗi khi đăng nhập", e);
            errorMessage.setValue("Có lỗi xảy ra khi đăng nhập: " + e.getMessage());
        } finally {
            isLoading.setValue(false);
        }
    }
    
    // ==================== USER LOGOUT ====================
    
    /**
     * Đăng xuất user hiện tại
     */
    public void logoutUser() {
        Log.d(TAG, "logoutUser: Đăng xuất user hiện tại");
        
        User user = currentUser.getValue();
        if (user != null) {
            Log.i(TAG, "logoutUser: Đăng xuất user: " + user.getUsername());
        }
        
        currentUser.setValue(null);
        isLoggedIn.setValue(false);
        errorMessage.setValue("Đã đăng xuất");
        
        Log.d(TAG, "logoutUser: Đăng xuất thành công");
    }
    
    // ==================== VALIDATION METHODS ====================
    
    /**
     * Validate dữ liệu đăng ký
     * @param username Tên đăng nhập
     * @param email Email
     * @param fullName Họ tên
     * @return true nếu hợp lệ, false nếu không
     */
    private boolean validateRegistrationInput(String username, String email, String fullName) {
        Log.d(TAG, "validateRegistrationInput: Bắt đầu validate input");
        
        if (username == null || username.trim().isEmpty()) {
            errorMessage.setValue("Vui lòng nhập tên đăng nhập");
            Log.w(TAG, "validateRegistrationInput: Username trống");
            return false;
        }
        
        if (username.trim().length() < 3) {
            errorMessage.setValue("Tên đăng nhập phải có ít nhất 3 ký tự");
            Log.w(TAG, "validateRegistrationInput: Username quá ngắn");
            return false;
        }
        
        if (email == null || email.trim().isEmpty()) {
            errorMessage.setValue("Vui lòng nhập email");
            Log.w(TAG, "validateRegistrationInput: Email trống");
            return false;
        }
        
        if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email.trim()).matches()) {
            errorMessage.setValue("Email không hợp lệ");
            Log.w(TAG, "validateRegistrationInput: Email không hợp lệ");
            return false;
        }
        
        if (fullName == null || fullName.trim().isEmpty()) {
            errorMessage.setValue("Vui lòng nhập họ tên");
            Log.w(TAG, "validateRegistrationInput: FullName trống");
            return false;
        }
        
        Log.d(TAG, "validateRegistrationInput: Validation thành công");
        return true;
    }
    
    // ==================== UTILITY METHODS ====================
    
    /**
     * Lấy ID của user hiện tại
     * @return ID của user hiện tại, -1 nếu chưa đăng nhập
     */
    public int getCurrentUserId() {
        User user = currentUser.getValue();
        int userId = user != null ? user.getUserId() : -1;
        Log.d(TAG, "getCurrentUserId: " + userId);
        return userId;
    }
    
    /**
     * Kiểm tra xem user đã đăng nhập chưa
     * @return true nếu đã đăng nhập, false nếu chưa
     */
    public boolean isUserLoggedIn() {
        Boolean loggedIn = isLoggedIn.getValue();
        boolean result = loggedIn != null && loggedIn;
        Log.d(TAG, "isUserLoggedIn: " + result);
        return result;
    }
    
    /**
     * Clear error message
     */
    public void clearErrorMessage() {
        Log.d(TAG, "clearErrorMessage: Xóa thông báo lỗi");
        errorMessage.setValue(null);
    }
}
