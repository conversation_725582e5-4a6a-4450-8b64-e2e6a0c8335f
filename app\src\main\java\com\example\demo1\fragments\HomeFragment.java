package com.example.demo1.fragments;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.GridLayoutManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.demo1.MainActivity;
import com.example.demo1.R;
import com.example.demo1.activities.AddEditTaskActivity;
import com.example.demo1.adapters.CategoryAdapter;
import com.example.demo1.adapters.TaskAdapter;
import com.example.demo1.adapters.IconSelectorAdapter;
import com.example.demo1.adapters.ColorSelectorAdapter;
import com.example.demo1.models.Category;
import com.example.demo1.models.Task;
import com.example.demo1.viewmodel.CategoryViewModel;
import com.example.demo1.viewmodel.TaskViewModel;
import com.example.demo1.viewmodel.UserViewModel;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.ArrayList;
import java.util.List;

/**
 * HomeFragment - Fragment chính hiển thị danh sách Category và Task
 * Chứa các chức năng chính: xem category, xem task, filter task, thêm task mới
 */
public class HomeFragment extends Fragment {
    
    private static final String TAG = "HomeFragment";
    
    // UI Components
    private RecyclerView rvCategories, rvTasks;
    private ChipGroup chipGroupFilter;
    private Chip chipAll, chipCompleted, chipIncomplete;
    private FloatingActionButton fabAddTask, fabAddCategory;
    
    // Adapters
    private CategoryAdapter categoryAdapter;
    private TaskAdapter taskAdapter;
    
    // ViewModels
    private UserViewModel userViewModel;
    private CategoryViewModel categoryViewModel;
    private TaskViewModel taskViewModel;
    
    // Data
    private List<Category> categories = new ArrayList<>();
    private List<Task> tasks = new ArrayList<>();
    private int currentUserId = -1;
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate: Khởi tạo HomeFragment");
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, 
                           @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "onCreateView: Tạo view cho HomeFragment");
        
        View view = inflater.inflate(R.layout.fragment_home, container, false);
        
        // Khởi tạo ViewModels
        initViewModels();
        
        // Khởi tạo UI
        initViews(view);
        
        // Thiết lập RecyclerViews
        setupRecyclerViews();
        
        // Thiết lập listeners
        setupListeners();
        
        // Thiết lập observers
        setupObservers();
        
        // Load dữ liệu ban đầu
        loadInitialData();
        
        Log.d(TAG, "onCreateView: Tạo view thành công");
        return view;
    }
    
    /**
     * Khởi tạo ViewModels
     */
    private void initViewModels() {
        Log.d(TAG, "initViewModels: Khởi tạo ViewModels");
        
        if (getActivity() == null) {
            Log.e(TAG, "initViewModels: Activity null");
            return;
        }
        
        try {
            userViewModel = new ViewModelProvider(requireActivity()).get(UserViewModel.class);
            categoryViewModel = new ViewModelProvider(requireActivity()).get(CategoryViewModel.class);
            taskViewModel = new ViewModelProvider(requireActivity()).get(TaskViewModel.class);
            
            Log.d(TAG, "initViewModels: Khởi tạo ViewModels thành công");
        } catch (Exception e) {
            Log.e(TAG, "initViewModels: Lỗi khi khởi tạo ViewModels", e);
            showToast("Có lỗi xảy ra khi khởi tạo");
        }
    }
    
    /**
     * Khởi tạo Views
     * @param view Root view
     */
    private void initViews(View view) {
        Log.d(TAG, "initViews: Khởi tạo Views");
        
        try {
            // Tìm các view
            rvCategories = view.findViewById(R.id.rv_categories);
            rvTasks = view.findViewById(R.id.rv_tasks);
            chipGroupFilter = view.findViewById(R.id.chip_group_filter);
            chipAll = view.findViewById(R.id.chip_all);
            chipCompleted = view.findViewById(R.id.chip_completed);
            chipIncomplete = view.findViewById(R.id.chip_incomplete);
            fabAddTask = view.findViewById(R.id.fab_add_task);
            fabAddCategory = view.findViewById(R.id.fab_add_category);
            
            // Kiểm tra các view bắt buộc
            if (rvCategories == null || rvTasks == null || fabAddTask == null) {
                Log.e(TAG, "initViews: Một số view bắt buộc không tìm thấy");
                showToast("Lỗi giao diện: Thiếu các thành phần cần thiết");
                return;
            }
            
            // Thiết lập chip filter mặc định
            if (chipAll != null) {
                chipAll.setChecked(true);
            }
            
            Log.d(TAG, "initViews: Khởi tạo Views thành công");
            
        } catch (Exception e) {
            Log.e(TAG, "initViews: Lỗi khi khởi tạo Views", e);
            showToast("Có lỗi xảy ra khi khởi tạo giao diện");
        }
    }
    
    /**
     * Thiết lập RecyclerViews
     */
    private void setupRecyclerViews() {
        Log.d(TAG, "setupRecyclerViews: Thiết lập RecyclerViews");
        
        if (getContext() == null) {
            Log.e(TAG, "setupRecyclerViews: Context null");
            return;
        }
        
        try {
            // Thiết lập Category RecyclerView
            if (rvCategories != null) {
                categoryAdapter = new CategoryAdapter(categories, this::onCategoryClick);
                rvCategories.setLayoutManager(new LinearLayoutManager(getContext(), 
                    LinearLayoutManager.HORIZONTAL, false));
                rvCategories.setAdapter(categoryAdapter);
                Log.d(TAG, "setupRecyclerViews: Thiết lập Category RecyclerView thành công");
            }
            
            // Thiết lập Task RecyclerView
            if (rvTasks != null) {
                taskAdapter = new TaskAdapter(tasks, this::onTaskClick, this::onTaskLongClick);
                rvTasks.setLayoutManager(new LinearLayoutManager(getContext()));
                rvTasks.setAdapter(taskAdapter);
                Log.d(TAG, "setupRecyclerViews: Thiết lập Task RecyclerView thành công");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "setupRecyclerViews: Lỗi khi thiết lập RecyclerViews", e);
            showToast("Có lỗi xảy ra khi thiết lập danh sách");
        }
    }
    
    /**
     * Thiết lập listeners
     */
    private void setupListeners() {
        Log.d(TAG, "setupListeners: Thiết lập listeners");
        
        // FAB thêm task
        if (fabAddTask != null) {
            fabAddTask.setOnClickListener(v -> {
                Log.d(TAG, "fabAddTask: Người dùng nhấn thêm task");
                openAddTaskActivity();
            });
        }
        
        // FAB thêm category
        if (fabAddCategory != null) {
            fabAddCategory.setOnClickListener(v -> {
                Log.d(TAG, "fabAddCategory: Người dùng nhấn thêm category");
                openAddCategoryDialog();
            });
        }
        
        // Chip filter listeners
        if (chipAll != null) {
            chipAll.setOnClickListener(v -> {
                Log.d(TAG, "chipAll: Lọc tất cả task");
                setTaskFilter("all");
            });
        }
        
        if (chipCompleted != null) {
            chipCompleted.setOnClickListener(v -> {
                Log.d(TAG, "chipCompleted: Lọc task đã hoàn thành");
                setTaskFilter("completed");
            });
        }
        
        if (chipIncomplete != null) {
            chipIncomplete.setOnClickListener(v -> {
                Log.d(TAG, "chipIncomplete: Lọc task chưa hoàn thành");
                setTaskFilter("incomplete");
            });
        }
        
        Log.d(TAG, "setupListeners: Thiết lập listeners thành công");
    }
    
    /**
     * Thiết lập observers
     */
    private void setupObservers() {
        Log.d(TAG, "setupObservers: Thiết lập observers");
        
        if (userViewModel == null || categoryViewModel == null || taskViewModel == null) {
            Log.e(TAG, "setupObservers: Một số ViewModel null");
            return;
        }
        
        // Observer cho user hiện tại
        userViewModel.getCurrentUser().observe(getViewLifecycleOwner(), user -> {
            if (user != null) {
                currentUserId = user.getUserId();
                Log.d(TAG, "Observer: Current user changed - userId: " + currentUserId);
                
                // Khởi tạo ViewModels với userId mới
                categoryViewModel.initWithUserId(currentUserId);
                taskViewModel.initWithUserId(currentUserId);
            }
        });
        
        // Observer cho categories
        categoryViewModel.getCategories().observe(getViewLifecycleOwner(), categoryList -> {
            Log.d(TAG, "Observer: Nhận được " + 
                (categoryList != null ? categoryList.size() : 0) + " categories");
            
            if (categoryList != null) {
                categories.clear();
                categories.addAll(categoryList);
                
                if (categoryAdapter != null) {
                    categoryAdapter.notifyDataSetChanged();
                }
            }
        });
        
        // Observer cho tasks (theo filter hiện tại)
        taskViewModel.getFilteredTasks().observe(getViewLifecycleOwner(), taskList -> {
            Log.d(TAG, "Observer: Nhận được " + 
                (taskList != null ? taskList.size() : 0) + " filtered tasks");
            
            if (taskList != null) {
                tasks.clear();
                tasks.addAll(taskList);
                
                if (taskAdapter != null) {
                    taskAdapter.notifyDataSetChanged();
                }
            }
        });
        
        // Observer cho selected category
        categoryViewModel.getSelectedCategory().observe(getViewLifecycleOwner(), category -> {
            if (category != null) {
                Log.d(TAG, "Observer: Category được chọn - " + category.getName());
                
                // Load tasks theo category
                if (category.isDefault()) {
                    // Category "Tất cả" -> load tất cả task
                    taskViewModel.setCategoryFilter(-1);
                } else {
                    // Category cụ thể -> load task theo category
                    taskViewModel.setCategoryFilter(category.getCategoryId());
                }
            }
        });
        
        // Observer cho error messages
        categoryViewModel.getErrorMessage().observe(getViewLifecycleOwner(), message -> {
            if (message != null && !message.isEmpty()) {
                Log.w(TAG, "Observer: Category error - " + message);
                showToast(message);
                categoryViewModel.clearErrorMessage();
            }
        });
        
        taskViewModel.getErrorMessage().observe(getViewLifecycleOwner(), message -> {
            if (message != null && !message.isEmpty()) {
                Log.w(TAG, "Observer: Task error - " + message);
                showToast(message);
                taskViewModel.clearErrorMessage();
            }
        });
        
        // Observer cho success messages
        categoryViewModel.getSuccessMessage().observe(getViewLifecycleOwner(), message -> {
            if (message != null && !message.isEmpty()) {
                Log.i(TAG, "Observer: Category success - " + message);
                showToast(message);
                categoryViewModel.clearSuccessMessage();
            }
        });
        
        taskViewModel.getSuccessMessage().observe(getViewLifecycleOwner(), message -> {
            if (message != null && !message.isEmpty()) {
                Log.i(TAG, "Observer: Task success - " + message);
                showToast(message);
                taskViewModel.clearSuccessMessage();
            }
        });
        
        Log.d(TAG, "setupObservers: Thiết lập observers thành công");
    }
    
    /**
     * Load dữ liệu ban đầu
     */
    private void loadInitialData() {
        Log.d(TAG, "loadInitialData: Load dữ liệu ban đầu");

        if (userViewModel == null) {
            Log.e(TAG, "loadInitialData: UserViewModel null");
            return;
        }

        currentUserId = userViewModel.getCurrentUserId();
        Log.d(TAG, "loadInitialData: Current userId = " + currentUserId);

        if (currentUserId > 0) {
            // Khởi tạo ViewModels với userId
            if (categoryViewModel != null) {
                categoryViewModel.initWithUserId(currentUserId);
            }
            if (taskViewModel != null) {
                taskViewModel.initWithUserId(currentUserId);
            }
        } else {
            Log.w(TAG, "loadInitialData: User chưa đăng nhập");
        }
    }

    // ==================== EVENT HANDLERS ====================

    /**
     * Xử lý khi click vào category
     * @param category Category được click
     */
    private void onCategoryClick(Category category) {
        Log.d(TAG, "onCategoryClick: Click vào category - " + category.getName());

        if (categoryViewModel != null) {
            categoryViewModel.selectCategory(category);
        }
    }

    /**
     * Xử lý khi click vào task
     * @param task Task được click
     */
    private void onTaskClick(Task task) {
        Log.d(TAG, "onTaskClick: Click vào task - " + task.getTitle());

        // Mở activity để edit task
        openEditTaskActivity(task.getTaskId());
    }

    /**
     * Xử lý khi long click vào task
     * @param task Task được long click
     */
    private void onTaskLongClick(Task task) {
        Log.d(TAG, "onTaskLongClick: Long click vào task - " + task.getTitle());

        // Hiển thị dialog xác nhận xóa
        showDeleteTaskDialog(task);
    }

    /**
     * Đặt filter cho task
     * @param filter "all", "completed", "incomplete"
     */
    private void setTaskFilter(String filter) {
        Log.d(TAG, "setTaskFilter: Đặt filter - " + filter);

        if (taskViewModel != null) {
            taskViewModel.setFilter(filter);
        }
    }

    /**
     * Mở activity thêm task mới
     */
    private void openAddTaskActivity() {
        Log.d(TAG, "openAddTaskActivity: Mở activity thêm task");

        if (getContext() == null) {
            Log.e(TAG, "openAddTaskActivity: Context null");
            return;
        }

        try {
            Intent intent = new Intent(getContext(), AddEditTaskActivity.class);

            // Truyền category được chọn (nếu có)
            if (categoryViewModel != null) {
                int selectedCategoryId = categoryViewModel.getSelectedCategoryId();
                if (selectedCategoryId > 0) {
                    intent.putExtra(AddEditTaskActivity.EXTRA_CATEGORY_ID, selectedCategoryId);
                }
            }

            startActivity(intent);
            Log.d(TAG, "openAddTaskActivity: Mở activity thành công");

        } catch (Exception e) {
            Log.e(TAG, "openAddTaskActivity: Lỗi khi mở activity", e);
            showToast("Có lỗi xảy ra khi mở màn hình thêm task");
        }
    }

    /**
     * Mở activity edit task
     * @param taskId ID của task cần edit
     */
    private void openEditTaskActivity(int taskId) {
        Log.d(TAG, "openEditTaskActivity: Mở activity edit task - taskId: " + taskId);

        if (getContext() == null) {
            Log.e(TAG, "openEditTaskActivity: Context null");
            return;
        }

        try {
            Intent intent = new Intent(getContext(), AddEditTaskActivity.class);
            intent.putExtra(AddEditTaskActivity.EXTRA_TASK_ID, taskId);
            startActivity(intent);

            Log.d(TAG, "openEditTaskActivity: Mở activity thành công");

        } catch (Exception e) {
            Log.e(TAG, "openEditTaskActivity: Lỗi khi mở activity", e);
            showToast("Có lỗi xảy ra khi mở màn hình sửa task");
        }
    }

    /**
     * Mở dialog thêm category
     */
    private void openAddCategoryDialog() {
        Log.d(TAG, "openAddCategoryDialog: Mở dialog thêm category");

        if (getContext() == null) {
            Log.e(TAG, "openAddCategoryDialog: Context null");
            return;
        }

        try {
            // Tạo layout cho dialog
            View dialogView = LayoutInflater.from(getContext()).inflate(R.layout.dialog_add_category, null);

            // Tìm các view trong dialog
            EditText etCategoryName = dialogView.findViewById(R.id.et_category_name);
            RecyclerView rvIcons = dialogView.findViewById(R.id.rv_icons);
            RecyclerView rvColors = dialogView.findViewById(R.id.rv_colors);

            if (etCategoryName == null || rvIcons == null || rvColors == null) {
                Log.e(TAG, "openAddCategoryDialog: Không tìm thấy view trong dialog");
                showToast("Lỗi giao diện dialog");
                return;
            }

            // Thiết lập icon selector
            List<String> icons = categoryViewModel != null ? categoryViewModel.getAvailableIcons() : new ArrayList<>();
            IconSelectorAdapter iconAdapter = new IconSelectorAdapter(icons, selectedIcon -> {
                Log.d(TAG, "openAddCategoryDialog: Chọn icon - " + selectedIcon);
            });
            rvIcons.setLayoutManager(new GridLayoutManager(getContext(), 6));
            rvIcons.setAdapter(iconAdapter);

            // Thiết lập color selector
            List<String> colors = categoryViewModel != null ? categoryViewModel.getAvailableColors() : new ArrayList<>();
            ColorSelectorAdapter colorAdapter = new ColorSelectorAdapter(colors, selectedColor -> {
                Log.d(TAG, "openAddCategoryDialog: Chọn màu - " + selectedColor);
            });
            rvColors.setLayoutManager(new GridLayoutManager(getContext(), 8));
            rvColors.setAdapter(colorAdapter);

            // Tạo dialog
            AlertDialog dialog = new AlertDialog.Builder(getContext())
                .setTitle("Thêm chủ đề mới")
                .setView(dialogView)
                .setPositiveButton("Thêm", null) // Set null để xử lý riêng
                .setNegativeButton("Hủy", (d, which) -> {
                    Log.d(TAG, "openAddCategoryDialog: Người dùng hủy");
                    d.dismiss();
                })
                .create();

            // Xử lý nút Thêm
            dialog.setOnShowListener(d -> {
                Button btnAdd = dialog.getButton(AlertDialog.BUTTON_POSITIVE);
                btnAdd.setOnClickListener(v -> {
                    String name = etCategoryName.getText().toString().trim();
                    String selectedIcon = iconAdapter.getSelectedIcon();
                    String selectedColor = colorAdapter.getSelectedColor();

                    Log.d(TAG, "openAddCategoryDialog: Thêm category - name: " + name +
                        ", icon: " + selectedIcon + ", color: " + selectedColor);

                    if (name.isEmpty()) {
                        etCategoryName.setError("Vui lòng nhập tên chủ đề");
                        etCategoryName.requestFocus();
                        return;
                    }

                    if (selectedIcon == null) {
                        showToast("Vui lòng chọn biểu tượng");
                        return;
                    }

                    if (selectedColor == null) {
                        showToast("Vui lòng chọn màu sắc");
                        return;
                    }

                    // Gọi ViewModel để thêm category
                    if (categoryViewModel != null) {
                        categoryViewModel.addCategory(name, selectedIcon, selectedColor);
                        dialog.dismiss();
                    }
                });
            });

            dialog.show();
            Log.d(TAG, "openAddCategoryDialog: Hiển thị dialog thành công");

        } catch (Exception e) {
            Log.e(TAG, "openAddCategoryDialog: Lỗi khi tạo dialog", e);
            showToast("Có lỗi xảy ra khi tạo dialog: " + e.getMessage());
        }
    }

    /**
     * Hiển thị dialog xác nhận xóa task
     * @param task Task cần xóa
     */
    private void showDeleteTaskDialog(Task task) {
        Log.d(TAG, "showDeleteTaskDialog: Hiển thị dialog xóa task - " + task.getTitle());

        if (getContext() == null) {
            Log.e(TAG, "showDeleteTaskDialog: Context null");
            return;
        }

        try {
            new androidx.appcompat.app.AlertDialog.Builder(getContext())
                .setTitle("Xác nhận xóa")
                .setMessage("Bạn có chắc chắn muốn xóa công việc \"" + task.getTitle() + "\"?")
                .setPositiveButton("Xóa", (dialog, which) -> {
                    Log.d(TAG, "showDeleteTaskDialog: Người dùng xác nhận xóa");
                    deleteTask(task);
                })
                .setNegativeButton("Hủy", (dialog, which) -> {
                    Log.d(TAG, "showDeleteTaskDialog: Người dùng hủy xóa");
                    dialog.dismiss();
                })
                .show();

        } catch (Exception e) {
            Log.e(TAG, "showDeleteTaskDialog: Lỗi khi hiển thị dialog", e);
            showToast("Có lỗi xảy ra khi hiển thị dialog");
        }
    }

    /**
     * Xóa task
     * @param task Task cần xóa
     */
    private void deleteTask(Task task) {
        Log.d(TAG, "deleteTask: Xóa task - " + task.getTitle());

        if (taskViewModel != null) {
            taskViewModel.deleteTask(task);
        }
    }

    // ==================== UTILITY METHODS ====================

    /**
     * Hiển thị Toast message
     * @param message Nội dung message
     */
    private void showToast(String message) {
        Log.d(TAG, "showToast: " + message);

        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        } else if (getActivity() instanceof MainActivity) {
            ((MainActivity) getActivity()).showToast(message);
        }
    }

    /**
     * Refresh dữ liệu
     */
    public void refreshData() {
        Log.d(TAG, "refreshData: Refresh dữ liệu");

        if (currentUserId > 0) {
            if (categoryViewModel != null) {
                categoryViewModel.initWithUserId(currentUserId);
            }
            if (taskViewModel != null) {
                taskViewModel.initWithUserId(currentUserId);
            }
        }
    }

    // ==================== LIFECYCLE METHODS ====================

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "onResume: HomeFragment resume");

        // Refresh dữ liệu khi quay lại fragment
        refreshData();
    }

    @Override
    public void onPause() {
        super.onPause();
        Log.d(TAG, "onPause: HomeFragment pause");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d(TAG, "onDestroyView: HomeFragment destroy view");

        // Cleanup
        if (categories != null) categories.clear();
        if (tasks != null) tasks.clear();
        categoryAdapter = null;
        taskAdapter = null;
    }
}
