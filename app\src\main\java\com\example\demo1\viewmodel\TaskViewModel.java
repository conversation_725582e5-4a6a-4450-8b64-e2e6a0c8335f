package com.example.demo1.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.example.demo1.models.Task;
import com.example.demo1.models.SubTask;
import com.example.demo1.repository.TodoRepository;
import com.example.demo1.utils.IconUtils;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * ViewModel cho Task và SubTask - Quản lý UI state và business logic
 * Chứa các chức năng CRUD task, subtask, filtering, và progress tracking
 */
public class TaskViewModel extends AndroidViewModel {
    
    private static final String TAG = "TaskViewModel";
    
    private TodoRepository repository;
    
    // LiveData cho danh sách task
    private MutableLiveData<List<Task>> tasks;
    private MutableLiveData<List<Task>> completedTasks;
    private MutableLiveData<List<Task>> incompleteTasks;
    
    // LiveData cho task được chọn
    private MutableLiveData<Task> selectedTask;
    
    // LiveData cho danh sách subtask
    private MutableLiveData<List<SubTask>> subTasks;
    
    // LiveData cho trạng thái loading
    private MutableLiveData<Boolean> isLoading;
    
    // LiveData cho thông báo lỗi
    private MutableLiveData<String> errorMessage;
    
    // LiveData cho thông báo thành công
    private MutableLiveData<String> successMessage;
    
    // LiveData cho progress
    private MutableLiveData<Integer> taskProgress;
    
    // Filter states
    private MutableLiveData<String> currentFilter; // "all", "completed", "incomplete"
    private MutableLiveData<Integer> currentCategoryFilter;
    
    // User ID hiện tại
    private int currentUserId = -1;
    
    /**
     * Constructor khởi tạo ViewModel
     * @param application Application context
     */
    public TaskViewModel(@NonNull Application application) {
        super(application);
        Log.d(TAG, "TaskViewModel: Khởi tạo TaskViewModel");
        
        // Khởi tạo repository
        repository = new TodoRepository(application);
        
        // Khởi tạo LiveData
        tasks = new MutableLiveData<>();
        completedTasks = new MutableLiveData<>();
        incompleteTasks = new MutableLiveData<>();
        selectedTask = new MutableLiveData<>();
        subTasks = new MutableLiveData<>();
        isLoading = new MutableLiveData<>(false);
        errorMessage = new MutableLiveData<>();
        successMessage = new MutableLiveData<>();
        taskProgress = new MutableLiveData<>(0);
        currentFilter = new MutableLiveData<>("all");
        currentCategoryFilter = new MutableLiveData<>(-1);
        
        Log.d(TAG, "TaskViewModel: Khởi tạo thành công tất cả LiveData");
    }
    
    // ==================== GETTER METHODS ====================
    
    public LiveData<List<Task>> getTasks() {
        return tasks;
    }
    
    public LiveData<List<Task>> getCompletedTasks() {
        return completedTasks;
    }
    
    public LiveData<List<Task>> getIncompleteTasks() {
        return incompleteTasks;
    }
    
    public LiveData<Task> getSelectedTask() {
        return selectedTask;
    }
    
    public LiveData<List<SubTask>> getSubTasks() {
        return subTasks;
    }
    
    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }
    
    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }
    
    public LiveData<String> getSuccessMessage() {
        return successMessage;
    }
    
    public LiveData<Integer> getTaskProgress() {
        return taskProgress;
    }
    
    public LiveData<String> getCurrentFilter() {
        return currentFilter;
    }
    
    // ==================== INITIALIZATION ====================
    
    /**
     * Khởi tạo ViewModel với user ID
     * @param userId ID của user hiện tại
     */
    public void initWithUserId(int userId) {
        Log.d(TAG, "initWithUserId: Khởi tạo với userId: " + userId);
        
        if (userId <= 0) {
            Log.e(TAG, "initWithUserId: UserId không hợp lệ: " + userId);
            errorMessage.setValue("User ID không hợp lệ");
            return;
        }
        
        this.currentUserId = userId;
        loadAllTasksForUser(userId);
        
        Log.d(TAG, "initWithUserId: Khởi tạo thành công cho user: " + userId);
    }
    
    /**
     * Load tất cả task cho user
     * @param userId ID của user
     */
    private void loadAllTasksForUser(int userId) {
        Log.d(TAG, "loadAllTasksForUser: Load task cho user: " + userId);
        
        // Load tất cả task
        LiveData<List<Task>> allTasksLiveData = repository.getTasksByUserId(userId);
        allTasksLiveData.observeForever(taskList -> {
            Log.d(TAG, "loadAllTasksForUser: Nhận được " + 
                (taskList != null ? taskList.size() : 0) + " tasks");
            tasks.setValue(taskList);
        });
        
        // Load completed tasks
        LiveData<List<Task>> completedTasksLiveData = repository.getCompletedTasksByUserId(userId);
        completedTasksLiveData.observeForever(taskList -> {
            Log.d(TAG, "loadAllTasksForUser: Nhận được " + 
                (taskList != null ? taskList.size() : 0) + " completed tasks");
            completedTasks.setValue(taskList);
        });
        
        // Load incomplete tasks
        LiveData<List<Task>> incompleteTasksLiveData = repository.getIncompleteTasksByUserId(userId);
        incompleteTasksLiveData.observeForever(taskList -> {
            Log.d(TAG, "loadAllTasksForUser: Nhận được " + 
                (taskList != null ? taskList.size() : 0) + " incomplete tasks");
            incompleteTasks.setValue(taskList);
        });
    }
    
    // ==================== TASK CRUD OPERATIONS ====================
    
    /**
     * Thêm task mới
     * @param title Tiêu đề task
     * @param description Mô tả task
     * @param categoryId ID category
     * @param priority Mức độ ưu tiên
     * @param dueDate Ngày hết hạn
     * @param subTaskTitles Danh sách tiêu đề subtask
     */
    public void addTask(String title, String description, int categoryId, String priority, 
                       long dueDate, List<String> subTaskTitles) {
        Log.d(TAG, "addTask: Thêm task mới - title: " + title + ", categoryId: " + categoryId + 
            ", priority: " + priority + ", subtasks: " + (subTaskTitles != null ? subTaskTitles.size() : 0));
        
        if (!validateTaskInput(title, description, categoryId, priority)) {
            Log.e(TAG, "addTask: Validation failed");
            return;
        }
        
        if (currentUserId <= 0) {
            errorMessage.setValue("Chưa đăng nhập");
            Log.e(TAG, "addTask: User chưa đăng nhập");
            return;
        }
        
        isLoading.setValue(true);
        
        try {
            // Tạo task mới
            Task newTask = new Task(currentUserId, categoryId, title.trim(), 
                description != null ? description.trim() : "", priority, dueDate);
            Log.d(TAG, "addTask: Tạo task mới - " + newTask.toString());
            
            long taskId = repository.insertTask(newTask).get();
            Log.d(TAG, "addTask: Thêm task thành công - taskId: " + taskId);
            
            // Thêm subtasks nếu có
            if (subTaskTitles != null && !subTaskTitles.isEmpty()) {
                addSubTasksToTask((int) taskId, subTaskTitles);
            }
            
            successMessage.setValue("Thêm công việc thành công!");
            Log.i(TAG, "addTask: Thêm task thành công: " + title);
            
        } catch (ExecutionException | InterruptedException e) {
            Log.e(TAG, "addTask: Lỗi khi thêm task", e);
            errorMessage.setValue("Có lỗi xảy ra khi thêm công việc: " + e.getMessage());
        } finally {
            isLoading.setValue(false);
        }
    }
    
    /**
     * Cập nhật task
     * @param task Task cần cập nhật
     * @param newTitle Tiêu đề mới
     * @param newDescription Mô tả mới
     * @param newCategoryId Category ID mới
     * @param newPriority Mức độ ưu tiên mới
     * @param newDueDate Ngày hết hạn mới
     */
    public void updateTask(Task task, String newTitle, String newDescription, 
                          int newCategoryId, String newPriority, long newDueDate) {
        Log.d(TAG, "updateTask: Cập nhật task - id: " + task.getTaskId() + 
            ", newTitle: " + newTitle + ", newCategoryId: " + newCategoryId);
        
        if (!validateTaskInput(newTitle, newDescription, newCategoryId, newPriority)) {
            Log.e(TAG, "updateTask: Validation failed");
            return;
        }
        
        isLoading.setValue(true);
        
        try {
            // Cập nhật thông tin task
            task.setTitle(newTitle.trim());
            task.setDescription(newDescription != null ? newDescription.trim() : "");
            task.setCategoryId(newCategoryId);
            task.setPriority(newPriority);
            task.setDueDate(newDueDate);
            
            Log.d(TAG, "updateTask: Cập nhật task - " + task.toString());
            
            repository.updateTask(task);
            
            successMessage.setValue("Cập nhật công việc thành công!");
            Log.i(TAG, "updateTask: Cập nhật task thành công: " + newTitle);
            
        } catch (Exception e) {
            Log.e(TAG, "updateTask: Lỗi khi cập nhật task", e);
            errorMessage.setValue("Có lỗi xảy ra khi cập nhật công việc: " + e.getMessage());
        } finally {
            isLoading.setValue(false);
        }
    }
    
    /**
     * Xóa task
     * @param task Task cần xóa
     */
    public void deleteTask(Task task) {
        Log.d(TAG, "deleteTask: Xóa task - id: " + task.getTaskId() + ", title: " + task.getTitle());
        
        isLoading.setValue(true);
        
        try {
            repository.deleteTask(task);
            
            // Clear selected task nếu đang được chọn
            if (selectedTask.getValue() != null && 
                selectedTask.getValue().getTaskId() == task.getTaskId()) {
                selectedTask.setValue(null);
                subTasks.setValue(null);
                taskProgress.setValue(0);
                Log.d(TAG, "deleteTask: Clear selected task");
            }
            
            successMessage.setValue("Xóa công việc thành công!");
            Log.i(TAG, "deleteTask: Xóa task thành công: " + task.getTitle());
            
        } catch (Exception e) {
            Log.e(TAG, "deleteTask: Lỗi khi xóa task", e);
            errorMessage.setValue("Có lỗi xảy ra khi xóa công việc: " + e.getMessage());
        } finally {
            isLoading.setValue(false);
        }
    }
    
    // ==================== SUBTASK OPERATIONS ====================
    
    /**
     * Thêm subtasks cho task
     * @param taskId ID của task
     * @param subTaskTitles Danh sách tiêu đề subtask
     */
    private void addSubTasksToTask(int taskId, List<String> subTaskTitles) {
        Log.d(TAG, "addSubTasksToTask: Thêm " + subTaskTitles.size() + " subtasks cho task: " + taskId);
        
        try {
            for (int i = 0; i < subTaskTitles.size(); i++) {
                String title = subTaskTitles.get(i);
                if (title != null && !title.trim().isEmpty()) {
                    SubTask subTask = new SubTask(taskId, title.trim(), "", i);
                    repository.insertSubTask(subTask);
                    Log.d(TAG, "addSubTasksToTask: Thêm subtask: " + title);
                }
            }
            Log.i(TAG, "addSubTasksToTask: Thêm thành công " + subTaskTitles.size() + " subtasks");
            
        } catch (Exception e) {
            Log.e(TAG, "addSubTasksToTask: Lỗi khi thêm subtasks", e);
        }
    }
    
    /**
     * Toggle trạng thái hoàn thành của subtask
     * @param subTask SubTask cần toggle
     */
    public void toggleSubTaskCompletion(SubTask subTask) {
        Log.d(TAG, "toggleSubTaskCompletion: Toggle subtask - id: " + subTask.getSubTaskId() + 
            ", current: " + subTask.isCompleted());
        
        boolean newStatus = !subTask.isCompleted();
        
        try {
            repository.updateSubTaskCompletionStatus(subTask.getSubTaskId(), newStatus);
            
            // Cập nhật trạng thái task dựa trên subtasks
            repository.updateTaskCompletionBasedOnSubTasks(subTask.getTaskId());
            
            // Cập nhật progress
            updateTaskProgress(subTask.getTaskId());
            
            Log.i(TAG, "toggleSubTaskCompletion: Toggle thành công - new status: " + newStatus);
            
        } catch (Exception e) {
            Log.e(TAG, "toggleSubTaskCompletion: Lỗi khi toggle subtask", e);
            errorMessage.setValue("Có lỗi xảy ra khi cập nhật subtask: " + e.getMessage());
        }
    }
    
    /**
     * Load subtasks cho task được chọn
     * @param taskId ID của task
     */
    public void loadSubTasksForTask(int taskId) {
        Log.d(TAG, "loadSubTasksForTask: Load subtasks cho task: " + taskId);
        
        LiveData<List<SubTask>> subTasksLiveData = repository.getSubTasksByTaskId(taskId);
        subTasksLiveData.observeForever(subTaskList -> {
            Log.d(TAG, "loadSubTasksForTask: Nhận được " + 
                (subTaskList != null ? subTaskList.size() : 0) + " subtasks");
            subTasks.setValue(subTaskList);
            
            // Cập nhật progress
            updateTaskProgress(taskId);
        });
    }
    
    /**
     * Cập nhật progress của task
     * @param taskId ID của task
     */
    private void updateTaskProgress(int taskId) {
        Log.d(TAG, "updateTaskProgress: Cập nhật progress cho task: " + taskId);

        try {
            repository.getTaskCompletionPercentage(taskId).thenAccept(percentage -> {
                Log.d(TAG, "updateTaskProgress: Progress = " + percentage + "%");
                taskProgress.postValue(percentage);
            });
        } catch (Exception e) {
            Log.e(TAG, "updateTaskProgress: Lỗi khi cập nhật progress", e);
        }
    }

    // ==================== TASK SELECTION ====================

    /**
     * Chọn task
     * @param task Task được chọn
     */
    public void selectTask(Task task) {
        Log.d(TAG, "selectTask: Chọn task - id: " + task.getTaskId() + ", title: " + task.getTitle());
        selectedTask.setValue(task);
        loadSubTasksForTask(task.getTaskId());
    }

    /**
     * Clear task được chọn
     */
    public void clearSelectedTask() {
        Log.d(TAG, "clearSelectedTask: Clear task được chọn");
        selectedTask.setValue(null);
        subTasks.setValue(null);
        taskProgress.setValue(0);
    }

    // ==================== FILTERING ====================

    /**
     * Lọc task theo trạng thái
     * @param filter "all", "completed", "incomplete"
     */
    public void setFilter(String filter) {
        Log.d(TAG, "setFilter: Đặt filter: " + filter);
        currentFilter.setValue(filter);
    }

    /**
     * Lọc task theo category
     * @param categoryId ID của category (-1 cho tất cả)
     */
    public void setCategoryFilter(int categoryId) {
        Log.d(TAG, "setCategoryFilter: Đặt category filter: " + categoryId);
        currentCategoryFilter.setValue(categoryId);

        if (currentUserId > 0) {
            if (categoryId == -1) {
                // Load tất cả task
                loadAllTasksForUser(currentUserId);
            } else {
                // Load task theo category
                loadTasksByCategory(categoryId);
            }
        }
    }

    /**
     * Load task theo category
     * @param categoryId ID của category
     */
    private void loadTasksByCategory(int categoryId) {
        Log.d(TAG, "loadTasksByCategory: Load task cho category: " + categoryId);

        LiveData<List<Task>> tasksLiveData = repository.getTasksByCategoryId(currentUserId, categoryId);
        tasksLiveData.observeForever(taskList -> {
            Log.d(TAG, "loadTasksByCategory: Nhận được " +
                (taskList != null ? taskList.size() : 0) + " tasks cho category: " + categoryId);
            tasks.setValue(taskList);
        });
    }

    /**
     * Lấy task theo filter hiện tại
     * @return LiveData chứa danh sách task đã filter
     */
    public LiveData<List<Task>> getFilteredTasks() {
        String filter = currentFilter.getValue();
        Log.d(TAG, "getFilteredTasks: Lấy task với filter: " + filter);

        switch (filter != null ? filter : "all") {
            case "completed":
                return completedTasks;
            case "incomplete":
                return incompleteTasks;
            default:
                return tasks;
        }
    }

    // ==================== DATE FILTERING ====================

    /**
     * Lọc task theo khoảng thời gian
     * @param startDate Ngày bắt đầu
     * @param endDate Ngày kết thúc
     */
    public void loadTasksByDateRange(long startDate, long endDate) {
        Log.d(TAG, "loadTasksByDateRange: Load task từ " + startDate + " đến " + endDate);

        if (currentUserId <= 0) {
            Log.e(TAG, "loadTasksByDateRange: User chưa đăng nhập");
            return;
        }

        LiveData<List<Task>> tasksLiveData = repository.getTasksByDateRange(currentUserId, startDate, endDate);
        tasksLiveData.observeForever(taskList -> {
            Log.d(TAG, "loadTasksByDateRange: Nhận được " +
                (taskList != null ? taskList.size() : 0) + " tasks trong khoảng thời gian");
            tasks.setValue(taskList);
        });
    }

    // ==================== VALIDATION METHODS ====================

    /**
     * Validate dữ liệu task
     * @param title Tiêu đề task
     * @param description Mô tả task
     * @param categoryId ID category
     * @param priority Mức độ ưu tiên
     * @return true nếu hợp lệ, false nếu không
     */
    private boolean validateTaskInput(String title, String description, int categoryId, String priority) {
        Log.d(TAG, "validateTaskInput: Validate input - title: " + title +
            ", categoryId: " + categoryId + ", priority: " + priority);

        if (title == null || title.trim().isEmpty()) {
            errorMessage.setValue("Vui lòng nhập tiêu đề công việc");
            Log.w(TAG, "validateTaskInput: Tiêu đề trống");
            return false;
        }

        if (title.trim().length() < 2) {
            errorMessage.setValue("Tiêu đề phải có ít nhất 2 ký tự");
            Log.w(TAG, "validateTaskInput: Tiêu đề quá ngắn");
            return false;
        }

        if (categoryId <= 0) {
            errorMessage.setValue("Vui lòng chọn chủ đề");
            Log.w(TAG, "validateTaskInput: Category không hợp lệ");
            return false;
        }

        if (!IconUtils.isValidPriority(priority)) {
            errorMessage.setValue("Mức độ ưu tiên không hợp lệ");
            Log.w(TAG, "validateTaskInput: Priority không hợp lệ: " + priority);
            return false;
        }

        Log.d(TAG, "validateTaskInput: Validation thành công");
        return true;
    }

    // ==================== UTILITY METHODS ====================

    /**
     * Clear error message
     */
    public void clearErrorMessage() {
        Log.d(TAG, "clearErrorMessage: Xóa thông báo lỗi");
        errorMessage.setValue(null);
    }

    /**
     * Clear success message
     */
    public void clearSuccessMessage() {
        Log.d(TAG, "clearSuccessMessage: Xóa thông báo thành công");
        successMessage.setValue(null);
    }

    /**
     * Lấy danh sách mức độ ưu tiên
     * @return Danh sách mức độ ưu tiên
     */
    public List<String> getPriorityLevels() {
        Log.d(TAG, "getPriorityLevels: Lấy danh sách mức độ ưu tiên");
        return IconUtils.PRIORITY_LEVELS;
    }

    /**
     * Lấy ID của task được chọn
     * @return ID của task được chọn, -1 nếu chưa chọn
     */
    public int getSelectedTaskId() {
        Task task = selectedTask.getValue();
        int taskId = task != null ? task.getTaskId() : -1;
        Log.d(TAG, "getSelectedTaskId: " + taskId);
        return taskId;
    }
}
