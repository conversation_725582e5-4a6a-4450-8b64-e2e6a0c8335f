<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".fragments.CalendarFragment">

    <!-- Header -->
    <TextView
        android:id="@+id/tv_header"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Lịch công việc"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:padding="16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Calendar View -->
    <CalendarView
        android:id="@+id/calendar_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_header"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Selected Date Info -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_date_info"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:cardBackgroundColor="@color/white"
        app:layout_constraintTop_toBottomOf="@+id/calendar_view"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:id="@+id/tv_selected_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                tools:text="Thứ Hai, 01/01/2024" />

            <TextView
                android:id="@+id/tv_task_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                tools:text="3 công việc" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Tasks for Selected Date -->
    <TextView
        android:id="@+id/tv_tasks_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Công việc trong ngày"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@+id/card_date_info"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_tasks"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:paddingHorizontal="16dp"
        android:paddingBottom="16dp"
        android:clipToPadding="false"
        app:layout_constraintTop_toBottomOf="@+id/tv_tasks_label"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:listitem="@layout/item_task" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/layout_empty_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@+id/rv_tasks"
        app:layout_constraintBottom_toBottomOf="@+id/rv_tasks"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📅"
            android:textSize="48sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Không có công việc nào"
            android:textSize="16sp"
            android:textColor="@color/text_secondary"
            android:layout_marginTop="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Chọn ngày khác để xem công việc"
            android:textSize="14sp"
            android:textColor="@color/text_hint"
            android:layout_marginTop="4dp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
