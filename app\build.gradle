plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.example.demo1'
    compileSdk 35

    defaultConfig {
        applicationId "com.example.demo1"
        minSdk 28
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    implementation libs.recyclerview

    // Room Database
    implementation libs.room.runtime
    annotationProcessor libs.room.compiler
    implementation libs.room.ktx

    // Lifecycle components
    implementation libs.lifecycle.viewmodel
    implementation libs.lifecycle.livedata
    implementation libs.lifecycle.common.java8

    // Navigation components
    implementation libs.navigation.fragment
    implementation libs.navigation.ui

    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}