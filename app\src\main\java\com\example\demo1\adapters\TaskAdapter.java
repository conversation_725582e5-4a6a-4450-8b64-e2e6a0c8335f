package com.example.demo1.adapters;

import android.graphics.Color;
import android.graphics.Paint;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.example.demo1.R;
import com.example.demo1.models.Task;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * TaskAdapter - Adapter cho RecyclerView hiển thị danh sách Task
 * Hiển thị task với thông tin chi tiết, progress bar và trạng thái hoàn thành
 */
public class TaskAdapter extends RecyclerView.Adapter<TaskAdapter.TaskViewHolder> {
    
    private static final String TAG = "TaskAdapter";
    
    // Interface cho click listeners
    public interface OnTaskClickListener {
        void onTaskClick(Task task);
    }
    
    public interface OnTaskLongClickListener {
        void onTaskLongClick(Task task);
    }
    
    // Data và listeners
    private List<Task> tasks;
    private OnTaskClickListener clickListener;
    private OnTaskLongClickListener longClickListener;
    
    /**
     * Constructor
     * @param tasks Danh sách task
     * @param clickListener Listener cho click event
     * @param longClickListener Listener cho long click event
     */
    public TaskAdapter(List<Task> tasks, OnTaskClickListener clickListener, 
                      OnTaskLongClickListener longClickListener) {
        this.tasks = tasks;
        this.clickListener = clickListener;
        this.longClickListener = longClickListener;
        Log.d(TAG, "TaskAdapter: Khởi tạo với " + 
            (tasks != null ? tasks.size() : 0) + " tasks");
    }
    
    @NonNull
    @Override
    public TaskViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        Log.d(TAG, "onCreateViewHolder: Tạo ViewHolder mới");
        
        View view = LayoutInflater.from(parent.getContext())
            .inflate(R.layout.item_task, parent, false);
        return new TaskViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull TaskViewHolder holder, int position) {
        if (tasks == null || position < 0 || position >= tasks.size()) {
            Log.e(TAG, "onBindViewHolder: Position không hợp lệ - " + position);
            return;
        }
        
        Task task = tasks.get(position);
        Log.d(TAG, "onBindViewHolder: Bind task - " + task.getTitle() + " tại position " + position);
        
        holder.bind(task);
    }
    
    @Override
    public int getItemCount() {
        int count = tasks != null ? tasks.size() : 0;
        Log.d(TAG, "getItemCount: " + count);
        return count;
    }
    
    /**
     * Cập nhật danh sách task
     * @param newTasks Danh sách task mới
     */
    public void updateTasks(List<Task> newTasks) {
        Log.d(TAG, "updateTasks: Cập nhật với " + 
            (newTasks != null ? newTasks.size() : 0) + " tasks");
        
        this.tasks = newTasks;
        notifyDataSetChanged();
    }
    
    /**
     * ViewHolder cho Task item
     */
    class TaskViewHolder extends RecyclerView.ViewHolder {
        
        private CardView cardView;
        private TextView tvTitle, tvDescription, tvPriority, tvDueDate, tvProgress;
        private ProgressBar progressBar;
        
        public TaskViewHolder(@NonNull View itemView) {
            super(itemView);
            Log.d(TAG, "TaskViewHolder: Khởi tạo ViewHolder");
            
            // Tìm các view
            cardView = itemView.findViewById(R.id.card_task);
            tvTitle = itemView.findViewById(R.id.tv_task_title);
            tvDescription = itemView.findViewById(R.id.tv_task_description);
            tvPriority = itemView.findViewById(R.id.tv_task_priority);
            tvDueDate = itemView.findViewById(R.id.tv_task_due_date);
            tvProgress = itemView.findViewById(R.id.tv_task_progress);
            progressBar = itemView.findViewById(R.id.progress_bar_task);
            
            // Kiểm tra các view bắt buộc
            if (cardView == null || tvTitle == null) {
                Log.e(TAG, "TaskViewHolder: Một số view bắt buộc không tìm thấy");
            }
            
            // Thiết lập click listeners
            itemView.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && clickListener != null && tasks != null) {
                    Task task = tasks.get(position);
                    Log.d(TAG, "TaskViewHolder: Click vào task - " + task.getTitle());
                    clickListener.onTaskClick(task);
                }
            });
            
            itemView.setOnLongClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && longClickListener != null && tasks != null) {
                    Task task = tasks.get(position);
                    Log.d(TAG, "TaskViewHolder: Long click vào task - " + task.getTitle());
                    longClickListener.onTaskLongClick(task);
                    return true;
                }
                return false;
            });
        }
        
        /**
         * Bind dữ liệu task vào view
         * @param task Task cần bind
         */
        public void bind(Task task) {
            Log.d(TAG, "TaskViewHolder.bind: Bind task - " + task.getTitle());
            
            try {
                // Hiển thị tiêu đề
                if (tvTitle != null) {
                    tvTitle.setText(task.getTitle());
                    
                    // Nếu task đã hoàn thành, gạch ngang tiêu đề
                    if (task.isCompleted()) {
                        tvTitle.setPaintFlags(tvTitle.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
                        tvTitle.setTextColor(Color.GRAY);
                    } else {
                        tvTitle.setPaintFlags(tvTitle.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
                        tvTitle.setTextColor(Color.BLACK);
                    }
                }
                
                // Hiển thị mô tả
                if (tvDescription != null) {
                    String description = task.getDescription();
                    if (description != null && !description.trim().isEmpty()) {
                        tvDescription.setText(description);
                        tvDescription.setVisibility(View.VISIBLE);
                    } else {
                        tvDescription.setVisibility(View.GONE);
                    }
                }
                
                // Hiển thị mức độ ưu tiên
                if (tvPriority != null) {
                    tvPriority.setText(task.getPriority());
                    setPriorityColor(tvPriority, task.getPriority());
                }
                
                // Hiển thị ngày hết hạn
                if (tvDueDate != null) {
                    if (task.getDueDate() > 0) {
                        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
                        String dateString = sdf.format(new Date(task.getDueDate()));
                        tvDueDate.setText("Hết hạn: " + dateString);
                        tvDueDate.setVisibility(View.VISIBLE);
                        
                        // Kiểm tra xem task có quá hạn không
                        if (task.getDueDate() < System.currentTimeMillis() && !task.isCompleted()) {
                            tvDueDate.setTextColor(Color.RED);
                        } else {
                            tvDueDate.setTextColor(Color.GRAY);
                        }
                    } else {
                        tvDueDate.setVisibility(View.GONE);
                    }
                }
                
                // Hiển thị progress (tạm thời set 0% - sẽ được cập nhật từ subtasks)
                setProgress(0); // TODO: Tính progress thực tế từ subtasks
                
                // Thiết lập màu card theo trạng thái
                setCardBackground(task);
                
                Log.d(TAG, "TaskViewHolder.bind: Bind thành công");
                
            } catch (Exception e) {
                Log.e(TAG, "TaskViewHolder.bind: Lỗi khi bind dữ liệu", e);
            }
        }
        
        /**
         * Thiết lập màu cho priority
         * @param textView TextView hiển thị priority
         * @param priority Mức độ ưu tiên
         */
        private void setPriorityColor(TextView textView, String priority) {
            Log.d(TAG, "TaskViewHolder.setPriorityColor: Thiết lập màu cho priority - " + priority);
            
            if (textView == null || priority == null) return;
            
            switch (priority) {
                case "Cao":
                    textView.setTextColor(Color.parseColor("#F44336")); // Red
                    textView.setBackgroundResource(R.drawable.bg_priority_high);
                    break;
                case "Trung bình":
                    textView.setTextColor(Color.parseColor("#FF9800")); // Orange
                    textView.setBackgroundResource(R.drawable.bg_priority_medium);
                    break;
                case "Thấp":
                    textView.setTextColor(Color.parseColor("#4CAF50")); // Green
                    textView.setBackgroundResource(R.drawable.bg_priority_low);
                    break;
                default:
                    textView.setTextColor(Color.GRAY);
                    textView.setBackgroundResource(R.drawable.bg_priority_medium);
                    break;
            }
        }
        
        /**
         * Thiết lập progress
         * @param percentage Phần trăm hoàn thành (0-100)
         */
        private void setProgress(int percentage) {
            Log.d(TAG, "TaskViewHolder.setProgress: Thiết lập progress - " + percentage + "%");
            
            if (progressBar != null) {
                progressBar.setProgress(percentage);
            }
            
            if (tvProgress != null) {
                tvProgress.setText(percentage + "%");
            }
        }
        
        /**
         * Thiết lập background cho card theo trạng thái task
         * @param task Task
         */
        private void setCardBackground(Task task) {
            Log.d(TAG, "TaskViewHolder.setCardBackground: Thiết lập background cho task - " + 
                task.getTitle() + ", completed: " + task.isCompleted());
            
            if (cardView == null) return;
            
            if (task.isCompleted()) {
                // Task đã hoàn thành - màu xanh nhạt
                cardView.setCardBackgroundColor(Color.parseColor("#E8F5E8"));
            } else {
                // Task chưa hoàn thành
                if (task.getDueDate() > 0 && task.getDueDate() < System.currentTimeMillis()) {
                    // Task quá hạn - màu đỏ nhạt
                    cardView.setCardBackgroundColor(Color.parseColor("#FFEBEE"));
                } else {
                    // Task bình thường - màu trắng
                    cardView.setCardBackgroundColor(Color.WHITE);
                }
            }
        }
    }
}
