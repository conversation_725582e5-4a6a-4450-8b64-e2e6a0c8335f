package com.example.demo1.activities;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.ViewModelProvider;

import com.example.demo1.MainActivity;
import com.example.demo1.R;
import com.example.demo1.viewmodel.UserViewModel;

/**
 * LoginActivity - <PERSON>àn hình đăng nhập và đăng ký
 * Cho phép người dùng đăng nhập hoặc tạo tài khoản mới
 */
public class LoginActivity extends AppCompatActivity {
    
    private static final String TAG = "LoginActivity";
    
    // UI Components
    private EditText etUsername, etPassword, etEmail, etFullName;
    private Button btnLogin, btnRegister, btnSwitchMode;
    private TextView tvTitle, tvSwitchText;
    private ProgressBar progressBar;
    private View layoutEmail, layoutFullName;
    
    // ViewModels
    private UserViewModel userViewModel;
    
    // State
    private boolean isLoginMode = true; // true = đăng nhập, false = đăng ký
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate: Khởi tạo LoginActivity");
        
        setContentView(R.layout.activity_login);
        
        // Khởi tạo ViewModels
        initViewModels();
        
        // Khởi tạo UI
        initViews();
        
        // Thiết lập listeners
        setupListeners();
        
        // Thiết lập observers
        setupObservers();
        
        // Thiết lập UI ban đầu (chế độ đăng nhập)
        updateUIMode();
        
        Log.d(TAG, "onCreate: Khởi tạo LoginActivity thành công");
    }
    
    /**
     * Khởi tạo ViewModels
     */
    private void initViewModels() {
        Log.d(TAG, "initViewModels: Khởi tạo ViewModels");
        
        try {
            userViewModel = new ViewModelProvider(this).get(UserViewModel.class);
            Log.d(TAG, "initViewModels: Khởi tạo UserViewModel thành công");
        } catch (Exception e) {
            Log.e(TAG, "initViewModels: Lỗi khi khởi tạo ViewModels", e);
            Toast.makeText(this, "Có lỗi xảy ra khi khởi tạo ứng dụng", Toast.LENGTH_LONG).show();
        }
    }
    
    /**
     * Khởi tạo Views
     */
    private void initViews() {
        Log.d(TAG, "initViews: Khởi tạo Views");
        
        try {
            // Tìm các view
            tvTitle = findViewById(R.id.tv_title);
            etUsername = findViewById(R.id.et_username);
            etPassword = findViewById(R.id.et_password);
            etEmail = findViewById(R.id.et_email);
            etFullName = findViewById(R.id.et_full_name);
            btnLogin = findViewById(R.id.btn_login);
            btnRegister = findViewById(R.id.btn_register);
            btnSwitchMode = findViewById(R.id.btn_switch_mode);
            tvSwitchText = findViewById(R.id.tv_switch_text);
            progressBar = findViewById(R.id.progress_bar);
            layoutEmail = findViewById(R.id.layout_email);
            layoutFullName = findViewById(R.id.layout_full_name);
            
            // Kiểm tra các view bắt buộc
            if (tvTitle == null || etUsername == null || etPassword == null || btnLogin == null ||
                btnRegister == null || btnSwitchMode == null || progressBar == null) {
                Log.e(TAG, "initViews: Một số view bắt buộc không tìm thấy trong layout");
                Toast.makeText(this, "Lỗi giao diện: Thiếu các thành phần cần thiết", Toast.LENGTH_LONG).show();
                return;
            }
            
            Log.d(TAG, "initViews: Khởi tạo Views thành công");
            
        } catch (Exception e) {
            Log.e(TAG, "initViews: Lỗi khi khởi tạo Views", e);
            Toast.makeText(this, "Có lỗi xảy ra khi khởi tạo giao diện", Toast.LENGTH_LONG).show();
        }
    }
    
    /**
     * Thiết lập listeners
     */
    private void setupListeners() {
        Log.d(TAG, "setupListeners: Thiết lập listeners");
        
        // Nút đăng nhập
        if (btnLogin != null) {
            btnLogin.setOnClickListener(v -> {
                Log.d(TAG, "btnLogin: Người dùng nhấn nút đăng nhập");
                performLogin();
            });
        }
        
        // Nút đăng ký
        if (btnRegister != null) {
            btnRegister.setOnClickListener(v -> {
                Log.d(TAG, "btnRegister: Người dùng nhấn nút đăng ký");
                performRegister();
            });
        }
        
        // Nút chuyển đổi chế độ
        if (btnSwitchMode != null) {
            btnSwitchMode.setOnClickListener(v -> {
                Log.d(TAG, "btnSwitchMode: Người dùng chuyển đổi chế độ");
                switchMode();
            });
        }
        
        Log.d(TAG, "setupListeners: Thiết lập listeners thành công");
    }
    
    /**
     * Thiết lập observers
     */
    private void setupObservers() {
        Log.d(TAG, "setupObservers: Thiết lập observers");
        
        if (userViewModel == null) {
            Log.e(TAG, "setupObservers: UserViewModel null");
            return;
        }
        
        // Observer cho trạng thái loading
        userViewModel.getIsLoading().observe(this, isLoading -> {
            Log.d(TAG, "Observer: Trạng thái loading thay đổi - " + isLoading);
            
            if (progressBar != null) {
                progressBar.setVisibility(isLoading != null && isLoading ? View.VISIBLE : View.GONE);
            }
            
            // Disable/enable buttons khi loading
            setButtonsEnabled(isLoading == null || !isLoading);
        });
        
        // Observer cho trạng thái đăng nhập
        userViewModel.getIsLoggedIn().observe(this, isLoggedIn -> {
            Log.d(TAG, "Observer: Trạng thái đăng nhập thay đổi - " + isLoggedIn);
            
            if (isLoggedIn != null && isLoggedIn) {
                // Đăng nhập thành công -> chuyển đến MainActivity
                navigateToMain();
            }
        });
        
        // Observer cho thông báo lỗi
        userViewModel.getErrorMessage().observe(this, errorMessage -> {
            if (errorMessage != null && !errorMessage.isEmpty()) {
                Log.w(TAG, "Observer: Nhận thông báo lỗi - " + errorMessage);
                Toast.makeText(this, errorMessage, Toast.LENGTH_LONG).show();
                userViewModel.clearErrorMessage();
            }
        });
        
        Log.d(TAG, "setupObservers: Thiết lập observers thành công");
    }
    
    /**
     * Cập nhật UI theo chế độ hiện tại
     */
    private void updateUIMode() {
        Log.d(TAG, "updateUIMode: Cập nhật UI - chế độ đăng nhập: " + isLoginMode);
        
        if (isLoginMode) {
            // Chế độ đăng nhập
            if (tvTitle != null) tvTitle.setText("Đăng Nhập");
            if (layoutEmail != null) layoutEmail.setVisibility(View.GONE);
            if (layoutFullName != null) layoutFullName.setVisibility(View.GONE);
            if (btnLogin != null) btnLogin.setVisibility(View.VISIBLE);
            if (btnRegister != null) btnRegister.setVisibility(View.GONE);
            if (tvSwitchText != null) tvSwitchText.setText("Chưa có tài khoản?");
            if (btnSwitchMode != null) btnSwitchMode.setText("Đăng ký ngay");
        } else {
            // Chế độ đăng ký
            if (tvTitle != null) tvTitle.setText("Đăng Ký");
            if (layoutEmail != null) layoutEmail.setVisibility(View.VISIBLE);
            if (layoutFullName != null) layoutFullName.setVisibility(View.VISIBLE);
            if (btnLogin != null) btnLogin.setVisibility(View.GONE);
            if (btnRegister != null) btnRegister.setVisibility(View.VISIBLE);
            if (tvSwitchText != null) tvSwitchText.setText("Đã có tài khoản?");
            if (btnSwitchMode != null) btnSwitchMode.setText("Đăng nhập");
        }
        
        // Clear input fields
        clearInputFields();
        
        Log.d(TAG, "updateUIMode: Cập nhật UI thành công");
    }
    
    /**
     * Chuyển đổi chế độ đăng nhập/đăng ký
     */
    private void switchMode() {
        Log.d(TAG, "switchMode: Chuyển từ " + (isLoginMode ? "đăng nhập" : "đăng ký") + 
            " sang " + (!isLoginMode ? "đăng nhập" : "đăng ký"));
        
        isLoginMode = !isLoginMode;
        updateUIMode();
    }
    
    /**
     * Thực hiện đăng nhập
     */
    private void performLogin() {
        Log.d(TAG, "performLogin: Bắt đầu đăng nhập");

        if (etUsername == null || etPassword == null || userViewModel == null) {
            Log.e(TAG, "performLogin: Thiếu components cần thiết");
            return;
        }

        String username = etUsername.getText().toString().trim();
        String password = etPassword.getText().toString().trim();
        Log.d(TAG, "performLogin: Username nhập vào - " + username);

        if (username.isEmpty()) {
            Toast.makeText(this, "Vui lòng nhập tên đăng nhập", Toast.LENGTH_SHORT).show();
            Log.w(TAG, "performLogin: Username trống");
            return;
        }

        if (password.isEmpty()) {
            Toast.makeText(this, "Vui lòng nhập mật khẩu", Toast.LENGTH_SHORT).show();
            Log.w(TAG, "performLogin: Password trống");
            return;
        }

        // Gọi ViewModel để đăng nhập
        userViewModel.loginUser(username, password);
    }
    
    /**
     * Thực hiện đăng ký
     */
    private void performRegister() {
        Log.d(TAG, "performRegister: Bắt đầu đăng ký");

        if (etUsername == null || etPassword == null || etEmail == null || etFullName == null || userViewModel == null) {
            Log.e(TAG, "performRegister: Thiếu components cần thiết");
            return;
        }

        String username = etUsername.getText().toString().trim();
        String password = etPassword.getText().toString().trim();
        String email = etEmail.getText().toString().trim();
        String fullName = etFullName.getText().toString().trim();

        Log.d(TAG, "performRegister: Thông tin đăng ký - username: " + username +
            ", email: " + email + ", fullName: " + fullName);

        if (username.isEmpty()) {
            Toast.makeText(this, "Vui lòng nhập tên đăng nhập", Toast.LENGTH_SHORT).show();
            return;
        }

        if (password.isEmpty()) {
            Toast.makeText(this, "Vui lòng nhập mật khẩu", Toast.LENGTH_SHORT).show();
            return;
        }

        if (email.isEmpty()) {
            Toast.makeText(this, "Vui lòng nhập email", Toast.LENGTH_SHORT).show();
            return;
        }

        if (fullName.isEmpty()) {
            Toast.makeText(this, "Vui lòng nhập họ và tên", Toast.LENGTH_SHORT).show();
            return;
        }

        // Gọi ViewModel để đăng ký
        userViewModel.registerUser(username, password, email, fullName);
    }
    
    /**
     * Chuyển đến MainActivity
     */
    private void navigateToMain() {
        Log.d(TAG, "navigateToMain: Chuyển đến MainActivity");
        
        try {
            Intent intent = new Intent(this, MainActivity.class);
            startActivity(intent);
            finish(); // Đóng LoginActivity
            
            Log.d(TAG, "navigateToMain: Chuyển đến MainActivity thành công");
        } catch (Exception e) {
            Log.e(TAG, "navigateToMain: Lỗi khi chuyển đến MainActivity", e);
            Toast.makeText(this, "Có lỗi xảy ra khi chuyển màn hình", Toast.LENGTH_LONG).show();
        }
    }
    
    /**
     * Enable/Disable buttons
     * @param enabled true để enable, false để disable
     */
    private void setButtonsEnabled(boolean enabled) {
        if (btnLogin != null) btnLogin.setEnabled(enabled);
        if (btnRegister != null) btnRegister.setEnabled(enabled);
        if (btnSwitchMode != null) btnSwitchMode.setEnabled(enabled);
    }
    
    /**
     * Clear tất cả input fields
     */
    private void clearInputFields() {
        Log.d(TAG, "clearInputFields: Xóa tất cả input fields");

        if (etUsername != null) etUsername.setText("");
        if (etPassword != null) etPassword.setText("");
        if (etEmail != null) etEmail.setText("");
        if (etFullName != null) etFullName.setText("");
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "onDestroy: LoginActivity destroy");
    }
}
