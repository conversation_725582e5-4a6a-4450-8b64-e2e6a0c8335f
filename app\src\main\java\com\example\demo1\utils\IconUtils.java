package com.example.demo1.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Utility class để quản lý danh sách icon có sẵn cho Category
 * Chứa các icon Material Design phổ biến và phù hợp cho TodoList
 */
public class IconUtils {
    
    // Danh sách tên icon có sẵn (sử dụng Material Icons)
    public static final List<String> AVAILABLE_ICONS = Arrays.asList(
            // Công việc và nhiệm vụ
            "work", "assignment", "task_alt", "checklist", "event_note",
            
            // Gia đình và cá nhân
            "home", "family_restroom", "child_care", "elderly", "pets",
            
            // Học tập và giáo dục
            "school", "book", "library_books", "quiz", "science",
            
            // Sức khỏe và thể thao
            "fitness_center", "sports", "local_hospital", "healing", "spa",
            
            // <PERSON><PERSON> sắm và tài chính
            "shopping_cart", "store", "payment", "account_balance", "savings",
            
            // Du lịch và giải trí
            "flight", "hotel", "camera_alt", "movie", "music_note",
            
            // Ăn uống
            "restaurant", "local_cafe", "cake", "local_pizza", "fastfood",
            
            // Giao thông
            "directions_car", "directions_bus", "motorcycle", "pedal_bike", "train",
            
            // Công nghệ
            "computer", "phone_android", "wifi", "cloud", "code",
            
            // Thời tiết và thiên nhiên
            "wb_sunny", "cloud", "nature", "park", "eco",
            
            // Cảm xúc và trạng thái
            "favorite", "star", "thumb_up", "celebration", "mood",
            
            // Công cụ và tiện ích
            "build", "settings", "lightbulb", "key", "lock",
            
            // Thể loại khác
            "category", "label", "bookmark", "flag", "priority_high"
    );
    
    // Danh sách màu sắc có sẵn cho Category
    public static final List<String> AVAILABLE_COLORS = Arrays.asList(
            "#F44336", // Red
            "#E91E63", // Pink
            "#9C27B0", // Purple
            "#673AB7", // Deep Purple
            "#3F51B5", // Indigo
            "#2196F3", // Blue
            "#03A9F4", // Light Blue
            "#00BCD4", // Cyan
            "#009688", // Teal
            "#4CAF50", // Green
            "#8BC34A", // Light Green
            "#CDDC39", // Lime
            "#FFEB3B", // Yellow
            "#FFC107", // Amber
            "#FF9800", // Orange
            "#FF5722", // Deep Orange
            "#795548", // Brown
            "#9E9E9E", // Grey
            "#607D8B"  // Blue Grey
    );
    
    // Danh sách mức độ ưu tiên
    public static final List<String> PRIORITY_LEVELS = Arrays.asList(
            "Thấp",
            "Trung bình", 
            "Cao"
    );
    
    /**
     * Kiểm tra xem icon có tồn tại trong danh sách không
     * @param iconName Tên icon cần kiểm tra
     * @return true nếu icon tồn tại, false nếu không
     */
    public static boolean isValidIcon(String iconName) {
        return AVAILABLE_ICONS.contains(iconName);
    }
    
    /**
     * Kiểm tra xem màu có hợp lệ không
     * @param color Mã màu hex cần kiểm tra
     * @return true nếu màu hợp lệ, false nếu không
     */
    public static boolean isValidColor(String color) {
        return AVAILABLE_COLORS.contains(color);
    }
    
    /**
     * Kiểm tra xem mức độ ưu tiên có hợp lệ không
     * @param priority Mức độ ưu tiên cần kiểm tra
     * @return true nếu hợp lệ, false nếu không
     */
    public static boolean isValidPriority(String priority) {
        return PRIORITY_LEVELS.contains(priority);
    }
    
    /**
     * Lấy icon mặc định cho category "Tất cả"
     * @return Tên icon mặc định
     */
    public static String getDefaultAllCategoryIcon() {
        return "category";
    }
    
    /**
     * Lấy màu mặc định cho category "Tất cả"
     * @return Mã màu hex mặc định
     */
    public static String getDefaultAllCategoryColor() {
        return "#2196F3"; // Blue
    }
    
    /**
     * Lấy icon mặc định cho category thông thường
     * @return Tên icon mặc định
     */
    public static String getDefaultCategoryIcon() {
        return "label";
    }
    
    /**
     * Lấy màu mặc định cho category thông thường
     * @return Mã màu hex mặc định
     */
    public static String getDefaultCategoryColor() {
        return "#4CAF50"; // Green
    }
}
