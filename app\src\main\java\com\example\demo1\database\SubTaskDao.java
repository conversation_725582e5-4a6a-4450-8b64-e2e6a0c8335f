package com.example.demo1.database;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.demo1.models.SubTask;

import java.util.List;

/**
 * DAO (Data Access Object) cho SubTask Entity
 * Chứa các phương thức để thao tác với bảng subtasks trong database
 */
@Dao
public interface SubTaskDao {
    
    /**
     * Thêm subtask mới vào database
     * @param subTask SubTask cần thêm
     * @return ID của subtask vừa được thêm
     */
    @Insert
    long insertSubTask(SubTask subTask);
    
    /**
     * Thêm nhiều subtask cùng lúc
     * @param subTasks Danh sách SubTask cần thêm
     * @return Danh sách ID của các subtask vừa được thêm
     */
    @Insert
    List<Long> insertSubTasks(List<SubTask> subTasks);
    
    /**
     * Cập nhật thông tin subtask
     * @param subTask SubTask cần cập nhật
     * @return Số dòng bị ảnh hưởng
     */
    @Update
    int updateSubTask(SubTask subTask);
    
    /**
     * Xóa subtask khỏi database
     * @param subTask SubTask cần xóa
     * @return Số dòng bị ảnh hưởng
     */
    @Delete
    int deleteSubTask(SubTask subTask);
    
    /**
     * Lấy tất cả subtask của một task
     * @param taskId ID của task
     * @return LiveData chứa danh sách subtask
     */
    @Query("SELECT * FROM subtasks WHERE task_id = :taskId ORDER BY order_index ASC, created_at ASC")
    LiveData<List<SubTask>> getSubTasksByTaskId(int taskId);
    
    /**
     * Lấy subtask theo ID
     * @param subTaskId ID của subtask
     * @return LiveData chứa subtask
     */
    @Query("SELECT * FROM subtasks WHERE subtask_id = :subTaskId")
    LiveData<SubTask> getSubTaskById(int subTaskId);
    
    /**
     * Lấy subtask đã hoàn thành của task
     * @param taskId ID của task
     * @return LiveData chứa danh sách subtask đã hoàn thành
     */
    @Query("SELECT * FROM subtasks WHERE task_id = :taskId AND is_completed = 1 ORDER BY completed_at DESC")
    LiveData<List<SubTask>> getCompletedSubTasksByTaskId(int taskId);
    
    /**
     * Lấy subtask chưa hoàn thành của task
     * @param taskId ID của task
     * @return LiveData chứa danh sách subtask chưa hoàn thành
     */
    @Query("SELECT * FROM subtasks WHERE task_id = :taskId AND is_completed = 0 ORDER BY order_index ASC")
    LiveData<List<SubTask>> getIncompleteSubTasksByTaskId(int taskId);
    
    /**
     * Đánh dấu subtask hoàn thành/chưa hoàn thành
     * @param subTaskId ID của subtask
     * @param isCompleted Trạng thái hoàn thành
     * @param completedAt Thời gian hoàn thành (0 nếu chưa hoàn thành)
     * @return Số dòng bị ảnh hưởng
     */
    @Query("UPDATE subtasks SET is_completed = :isCompleted, completed_at = :completedAt WHERE subtask_id = :subTaskId")
    int updateSubTaskCompletionStatus(int subTaskId, boolean isCompleted, long completedAt);
    
    /**
     * Đếm tổng số subtask của task
     * @param taskId ID của task
     * @return Số lượng subtask
     */
    @Query("SELECT COUNT(*) FROM subtasks WHERE task_id = :taskId")
    LiveData<Integer> getSubTaskCountByTaskId(int taskId);
    
    /**
     * Đếm số subtask đã hoàn thành của task
     * @param taskId ID của task
     * @return Số lượng subtask đã hoàn thành
     */
    @Query("SELECT COUNT(*) FROM subtasks WHERE task_id = :taskId AND is_completed = 1")
    LiveData<Integer> getCompletedSubTaskCountByTaskId(int taskId);
    
    /**
     * Đếm số subtask chưa hoàn thành của task
     * @param taskId ID của task
     * @return Số lượng subtask chưa hoàn thành
     */
    @Query("SELECT COUNT(*) FROM subtasks WHERE task_id = :taskId AND is_completed = 0")
    LiveData<Integer> getIncompleteSubTaskCountByTaskId(int taskId);
    
    /**
     * Lấy tổng số subtask của task
     * @param taskId ID của task
     * @return Tổng số subtask
     */
    @Query("SELECT COUNT(*) FROM subtasks WHERE task_id = :taskId")
    int getTotalSubTaskCount(int taskId);

    /**
     * Lấy số subtask đã hoàn thành của task
     * @param taskId ID của task
     * @return Số subtask đã hoàn thành
     */
    @Query("SELECT COUNT(*) FROM subtasks WHERE task_id = :taskId AND is_completed = 1")
    int getCompletedSubTaskCount(int taskId);
    
    /**
     * Xóa tất cả subtask của task (khi xóa task)
     * @param taskId ID của task
     * @return Số dòng bị ảnh hưởng
     */
    @Query("DELETE FROM subtasks WHERE task_id = :taskId")
    int deleteSubTasksByTaskId(int taskId);
    
    /**
     * Cập nhật thứ tự hiển thị của subtask
     * @param subTaskId ID của subtask
     * @param orderIndex Thứ tự mới
     * @return Số dòng bị ảnh hưởng
     */
    @Query("UPDATE subtasks SET order_index = :orderIndex WHERE subtask_id = :subTaskId")
    int updateSubTaskOrder(int subTaskId, int orderIndex);
    
    /**
     * Lấy thứ tự cao nhất của subtask trong task
     * @param taskId ID của task
     * @return Thứ tự cao nhất
     */
    @Query("SELECT MAX(order_index) FROM subtasks WHERE task_id = :taskId")
    int getMaxOrderIndexByTaskId(int taskId);
}
