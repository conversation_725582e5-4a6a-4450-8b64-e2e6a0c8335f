{"logs": [{"outputFile": "com.example.demo1.app-mergeDebugResources-46:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f96c501bd1ba1d6317adff2fbcd632aa\\transformed\\material-1.10.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,348,433,518,633,743,844,985,1069,1133,1227,1297,1358,1445,1508,1572,1631,1705,1767,1821,1938,1996,2057,2111,2185,2307,2391,2487,2619,2697,2775,2904,2993,3073,3134,3189,3255,3324,3401,3488,3569,3643,3719,3809,3882,3984,4069,4148,4238,4330,4404,4489,4579,4631,4715,4780,4865,4950,5012,5076,5139,5208,5325,5433,5533,5637,5702,5761", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,82,84,84,114,109,100,140,83,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,95,131,77,77,128,88,79,60,54,65,68,76,86,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,107,99,103,64,58,81", "endOffsets": "260,343,428,513,628,738,839,980,1064,1128,1222,1292,1353,1440,1503,1567,1626,1700,1762,1816,1933,1991,2052,2106,2180,2302,2386,2482,2614,2692,2770,2899,2988,3068,3129,3184,3250,3319,3396,3483,3564,3638,3714,3804,3877,3979,4064,4143,4233,4325,4399,4484,4574,4626,4710,4775,4860,4945,5007,5071,5134,5203,5320,5428,5528,5632,5697,5756,5838"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3027,3110,3195,3280,3395,3505,3606,3747,3831,3895,3989,4059,4120,4207,4270,4334,4393,4467,4529,4583,4700,4758,4819,4873,4947,5069,5153,5249,5381,5459,5537,5666,5755,5835,5896,5951,6017,6086,6163,6250,6331,6405,6481,6571,6644,6746,6831,6910,7000,7092,7166,7251,7341,7393,7477,7542,7627,7712,7774,7838,7901,7970,8087,8195,8295,8399,8464,8751", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,102", "endColumns": "12,82,84,84,114,109,100,140,83,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,95,131,77,77,128,88,79,60,54,65,68,76,86,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,107,99,103,64,58,81", "endOffsets": "310,3105,3190,3275,3390,3500,3601,3742,3826,3890,3984,4054,4115,4202,4265,4329,4388,4462,4524,4578,4695,4753,4814,4868,4942,5064,5148,5244,5376,5454,5532,5661,5750,5830,5891,5946,6012,6081,6158,6245,6326,6400,6476,6566,6639,6741,6826,6905,6995,7087,7161,7246,7336,7388,7472,7537,7622,7707,7769,7833,7896,7965,8082,8190,8290,8394,8459,8518,8828"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\306de9d3f8c563e7b276242941a9a359\\transformed\\appcompat-1.6.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,426,535,647,732,837,954,1033,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2236,2341,2439,2546,2649,2764,2925,8833", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "421,530,642,727,832,949,1028,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2231,2336,2434,2541,2644,2759,2920,3022,8910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2da51fb8d78929212dd6b1464ada4ac4\\transformed\\core-1.9.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "8915", "endColumns": "100", "endOffsets": "9011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2781b657211557e5dc71f94e7a48b4d1\\transformed\\navigation-ui-2.7.6\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,117", "endOffsets": "160,278"}, "to": {"startLines": "100,101", "startColumns": "4,4", "startOffsets": "8523,8633", "endColumns": "109,117", "endOffsets": "8628,8746"}}]}]}