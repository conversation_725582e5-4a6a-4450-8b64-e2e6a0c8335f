package com.example.demo1.fragments;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CalendarView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.demo1.MainActivity;
import com.example.demo1.R;
import com.example.demo1.adapters.TaskAdapter;
import com.example.demo1.models.Task;
import com.example.demo1.viewmodel.TaskViewModel;
import com.example.demo1.viewmodel.UserViewModel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * CalendarFragment - Fragment hiển thị lịch và task theo ngày
 * Cho phép người dùng xem task theo ngày được chọn trên lịch
 */
public class CalendarFragment extends Fragment {
    
    private static final String TAG = "CalendarFragment";
    
    // UI Components
    private CalendarView calendarView;
    private TextView tvSelectedDate, tvTaskCount;
    private RecyclerView rvTasks;
    
    // Adapters
    private TaskAdapter taskAdapter;
    
    // ViewModels
    private UserViewModel userViewModel;
    private TaskViewModel taskViewModel;
    
    // Data
    private List<Task> tasks = new ArrayList<>();
    private long selectedDate = 0;
    private int currentUserId = -1;
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate: Khởi tạo CalendarFragment");
        
        // Đặt ngày được chọn mặc định là hôm nay
        selectedDate = System.currentTimeMillis();
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, 
                           @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "onCreateView: Tạo view cho CalendarFragment");
        
        View view = inflater.inflate(R.layout.fragment_calendar, container, false);
        
        // Khởi tạo ViewModels
        initViewModels();
        
        // Khởi tạo UI
        initViews(view);
        
        // Thiết lập RecyclerView
        setupRecyclerView();
        
        // Thiết lập listeners
        setupListeners();
        
        // Thiết lập observers
        setupObservers();
        
        // Load dữ liệu ban đầu
        loadInitialData();
        
        Log.d(TAG, "onCreateView: Tạo view thành công");
        return view;
    }
    
    /**
     * Khởi tạo ViewModels
     */
    private void initViewModels() {
        Log.d(TAG, "initViewModels: Khởi tạo ViewModels");
        
        if (getActivity() == null) {
            Log.e(TAG, "initViewModels: Activity null");
            return;
        }
        
        try {
            userViewModel = new ViewModelProvider(requireActivity()).get(UserViewModel.class);
            taskViewModel = new ViewModelProvider(requireActivity()).get(TaskViewModel.class);
            
            Log.d(TAG, "initViewModels: Khởi tạo ViewModels thành công");
        } catch (Exception e) {
            Log.e(TAG, "initViewModels: Lỗi khi khởi tạo ViewModels", e);
            showToast("Có lỗi xảy ra khi khởi tạo");
        }
    }
    
    /**
     * Khởi tạo Views
     * @param view Root view
     */
    private void initViews(View view) {
        Log.d(TAG, "initViews: Khởi tạo Views");
        
        try {
            // Tìm các view
            calendarView = view.findViewById(R.id.calendar_view);
            tvSelectedDate = view.findViewById(R.id.tv_selected_date);
            tvTaskCount = view.findViewById(R.id.tv_task_count);
            rvTasks = view.findViewById(R.id.rv_tasks);
            
            // Kiểm tra các view bắt buộc
            if (calendarView == null || rvTasks == null) {
                Log.e(TAG, "initViews: Một số view bắt buộc không tìm thấy");
                showToast("Lỗi giao diện: Thiếu các thành phần cần thiết");
                return;
            }
            
            // Thiết lập ngày hiện tại
            updateSelectedDateDisplay();
            
            Log.d(TAG, "initViews: Khởi tạo Views thành công");
            
        } catch (Exception e) {
            Log.e(TAG, "initViews: Lỗi khi khởi tạo Views", e);
            showToast("Có lỗi xảy ra khi khởi tạo giao diện");
        }
    }
    
    /**
     * Thiết lập RecyclerView
     */
    private void setupRecyclerView() {
        Log.d(TAG, "setupRecyclerView: Thiết lập RecyclerView");
        
        if (getContext() == null || rvTasks == null) {
            Log.e(TAG, "setupRecyclerView: Context hoặc RecyclerView null");
            return;
        }
        
        try {
            taskAdapter = new TaskAdapter(tasks, this::onTaskClick, this::onTaskLongClick);
            rvTasks.setLayoutManager(new LinearLayoutManager(getContext()));
            rvTasks.setAdapter(taskAdapter);
            
            Log.d(TAG, "setupRecyclerView: Thiết lập RecyclerView thành công");
            
        } catch (Exception e) {
            Log.e(TAG, "setupRecyclerView: Lỗi khi thiết lập RecyclerView", e);
            showToast("Có lỗi xảy ra khi thiết lập danh sách");
        }
    }
    
    /**
     * Thiết lập listeners
     */
    private void setupListeners() {
        Log.d(TAG, "setupListeners: Thiết lập listeners");
        
        // Calendar listener
        if (calendarView != null) {
            calendarView.setOnDateChangeListener((view, year, month, dayOfMonth) -> {
                Calendar calendar = Calendar.getInstance();
                calendar.set(year, month, dayOfMonth, 0, 0, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                
                selectedDate = calendar.getTimeInMillis();
                Log.d(TAG, "Calendar: Ngày được chọn - " + new Date(selectedDate));
                
                updateSelectedDateDisplay();
                loadTasksForSelectedDate();
            });
        }
        
        Log.d(TAG, "setupListeners: Thiết lập listeners thành công");
    }
    
    /**
     * Thiết lập observers
     */
    private void setupObservers() {
        Log.d(TAG, "setupObservers: Thiết lập observers");
        
        if (userViewModel == null || taskViewModel == null) {
            Log.e(TAG, "setupObservers: Một số ViewModel null");
            return;
        }
        
        // Observer cho user hiện tại
        userViewModel.getCurrentUser().observe(getViewLifecycleOwner(), user -> {
            if (user != null) {
                currentUserId = user.getUserId();
                Log.d(TAG, "Observer: Current user changed - userId: " + currentUserId);
                
                // Khởi tạo TaskViewModel với userId mới
                taskViewModel.initWithUserId(currentUserId);
                
                // Load tasks cho ngày được chọn
                loadTasksForSelectedDate();
            }
        });
        
        // Observer cho tasks
        taskViewModel.getTasks().observe(getViewLifecycleOwner(), taskList -> {
            Log.d(TAG, "Observer: Nhận được " + 
                (taskList != null ? taskList.size() : 0) + " tasks");
            
            if (taskList != null) {
                tasks.clear();
                tasks.addAll(taskList);
                
                if (taskAdapter != null) {
                    taskAdapter.notifyDataSetChanged();
                }
                
                updateTaskCountDisplay();
            }
        });
        
        // Observer cho error messages
        taskViewModel.getErrorMessage().observe(getViewLifecycleOwner(), message -> {
            if (message != null && !message.isEmpty()) {
                Log.w(TAG, "Observer: Task error - " + message);
                showToast(message);
                taskViewModel.clearErrorMessage();
            }
        });
        
        // Observer cho success messages
        taskViewModel.getSuccessMessage().observe(getViewLifecycleOwner(), message -> {
            if (message != null && !message.isEmpty()) {
                Log.i(TAG, "Observer: Task success - " + message);
                showToast(message);
                taskViewModel.clearSuccessMessage();
            }
        });
        
        Log.d(TAG, "setupObservers: Thiết lập observers thành công");
    }
    
    /**
     * Load dữ liệu ban đầu
     */
    private void loadInitialData() {
        Log.d(TAG, "loadInitialData: Load dữ liệu ban đầu");
        
        if (userViewModel == null) {
            Log.e(TAG, "loadInitialData: UserViewModel null");
            return;
        }
        
        currentUserId = userViewModel.getCurrentUserId();
        Log.d(TAG, "loadInitialData: Current userId = " + currentUserId);
        
        if (currentUserId > 0) {
            // Khởi tạo TaskViewModel với userId
            if (taskViewModel != null) {
                taskViewModel.initWithUserId(currentUserId);
            }
            
            // Load tasks cho ngày hiện tại
            loadTasksForSelectedDate();
        } else {
            Log.w(TAG, "loadInitialData: User chưa đăng nhập");
        }
    }
    
    /**
     * Load tasks cho ngày được chọn
     */
    private void loadTasksForSelectedDate() {
        Log.d(TAG, "loadTasksForSelectedDate: Load tasks cho ngày " + new Date(selectedDate));
        
        if (taskViewModel == null || currentUserId <= 0) {
            Log.w(TAG, "loadTasksForSelectedDate: TaskViewModel null hoặc user chưa đăng nhập");
            return;
        }
        
        // Tính toán start và end của ngày
        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTimeInMillis(selectedDate);
        startCalendar.set(Calendar.HOUR_OF_DAY, 0);
        startCalendar.set(Calendar.MINUTE, 0);
        startCalendar.set(Calendar.SECOND, 0);
        startCalendar.set(Calendar.MILLISECOND, 0);
        
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTimeInMillis(selectedDate);
        endCalendar.set(Calendar.HOUR_OF_DAY, 23);
        endCalendar.set(Calendar.MINUTE, 59);
        endCalendar.set(Calendar.SECOND, 59);
        endCalendar.set(Calendar.MILLISECOND, 999);
        
        long startDate = startCalendar.getTimeInMillis();
        long endDate = endCalendar.getTimeInMillis();
        
        Log.d(TAG, "loadTasksForSelectedDate: Khoảng thời gian từ " + 
            new Date(startDate) + " đến " + new Date(endDate));
        
        // Load tasks trong khoảng thời gian
        taskViewModel.loadTasksByDateRange(startDate, endDate);
    }
    
    /**
     * Cập nhật hiển thị ngày được chọn
     */
    private void updateSelectedDateDisplay() {
        Log.d(TAG, "updateSelectedDateDisplay: Cập nhật hiển thị ngày");
        
        if (tvSelectedDate != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("EEEE, dd/MM/yyyy", Locale.getDefault());
            String dateString = sdf.format(new Date(selectedDate));
            tvSelectedDate.setText(dateString);
            
            Log.d(TAG, "updateSelectedDateDisplay: " + dateString);
        }
    }
    
    /**
     * Cập nhật hiển thị số lượng task
     */
    private void updateTaskCountDisplay() {
        Log.d(TAG, "updateTaskCountDisplay: Cập nhật số lượng task");
        
        if (tvTaskCount != null) {
            int taskCount = tasks.size();
            String countText = taskCount == 0 ? "Không có công việc" : 
                taskCount + " công việc";
            tvTaskCount.setText(countText);
            
            Log.d(TAG, "updateTaskCountDisplay: " + countText);
        }
    }
    
    // ==================== EVENT HANDLERS ====================
    
    /**
     * Xử lý khi click vào task
     * @param task Task được click
     */
    private void onTaskClick(Task task) {
        Log.d(TAG, "onTaskClick: Click vào task - " + task.getTitle());
        
        // TODO: Mở activity để xem chi tiết task
        showToast("Chi tiết task: " + task.getTitle());
    }
    
    /**
     * Xử lý khi long click vào task
     * @param task Task được long click
     */
    private void onTaskLongClick(Task task) {
        Log.d(TAG, "onTaskLongClick: Long click vào task - " + task.getTitle());
        
        // TODO: Hiển thị menu context hoặc dialog
        showToast("Long click: " + task.getTitle());
    }
    
    // ==================== UTILITY METHODS ====================
    
    /**
     * Hiển thị Toast message
     * @param message Nội dung message
     */
    private void showToast(String message) {
        Log.d(TAG, "showToast: " + message);
        
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        } else if (getActivity() instanceof MainActivity) {
            ((MainActivity) getActivity()).showToast(message);
        }
    }
    
    /**
     * Refresh dữ liệu
     */
    public void refreshData() {
        Log.d(TAG, "refreshData: Refresh dữ liệu");
        
        loadTasksForSelectedDate();
    }
    
    // ==================== LIFECYCLE METHODS ====================
    
    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "onResume: CalendarFragment resume");
        
        // Refresh dữ liệu khi quay lại fragment
        refreshData();
    }
    
    @Override
    public void onPause() {
        super.onPause();
        Log.d(TAG, "onPause: CalendarFragment pause");
    }
    
    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d(TAG, "onDestroyView: CalendarFragment destroy view");
        
        // Cleanup
        if (tasks != null) tasks.clear();
        taskAdapter = null;
    }
}
