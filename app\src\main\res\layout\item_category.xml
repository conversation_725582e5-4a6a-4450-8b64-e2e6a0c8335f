<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/card_category"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@color/primary_light">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="16dp"
        android:minWidth="80dp">

        <!-- Category Icon -->
        <TextView
            android:id="@+id/tv_category_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="24sp"
            android:gravity="center"
            tools:text="📁" />

        <!-- Category Name -->
        <TextView
            android:id="@+id/tv_category_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="@color/primary"
            android:gravity="center"
            android:maxLines="2"
            android:ellipsize="end"
            tools:text="Công việc" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
