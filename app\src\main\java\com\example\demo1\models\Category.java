package com.example.demo1.models;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;
import androidx.room.ForeignKey;

/**
 * Entity Category - Đ<PERSON><PERSON> diện cho chủ đề/danh mục của các Task
 * Mỗi Category sẽ chứa nhiều Task, có icon và màu sắc riêng
 */
@Entity(tableName = "categories",
        foreignKeys = @ForeignKey(
                entity = User.class,
                parentColumns = "user_id",
                childColumns = "user_id",
                onDelete = ForeignKey.CASCADE
        ))
public class Category {
    
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "category_id")
    private int categoryId;
    
    @ColumnInfo(name = "user_id")
    private int userId; // Liên kết với User
    
    @ColumnInfo(name = "name")
    private String name;
    
    @ColumnInfo(name = "icon_name")
    private String iconName; // Tên icon từ danh sách có sẵn
    
    @ColumnInfo(name = "color")
    private String color; // Mã màu hex (ví dụ: #FF5722)
    
    @ColumnInfo(name = "created_at")
    private long createdAt;
    
    @ColumnInfo(name = "is_default")
    private boolean isDefault; // Category "Tất cả" sẽ là true
    
    // Constructor mặc định
    public Category() {
        this.createdAt = System.currentTimeMillis();
        this.isDefault = false;
    }
    
    // Constructor với tham số
    public Category(int userId, String name, String iconName, String color) {
        this.userId = userId;
        this.name = name;
        this.iconName = iconName;
        this.color = color;
        this.createdAt = System.currentTimeMillis();
        this.isDefault = false;
    }
    
    // Constructor cho category mặc định "Tất cả"
    public Category(int userId, String name, String iconName, String color, boolean isDefault) {
        this.userId = userId;
        this.name = name;
        this.iconName = iconName;
        this.color = color;
        this.createdAt = System.currentTimeMillis();
        this.isDefault = isDefault;
    }
    
    // Getter và Setter methods
    public int getCategoryId() {
        return categoryId;
    }
    
    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }
    
    public int getUserId() {
        return userId;
    }
    
    public void setUserId(int userId) {
        this.userId = userId;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getIconName() {
        return iconName;
    }
    
    public void setIconName(String iconName) {
        this.iconName = iconName;
    }
    
    public String getColor() {
        return color;
    }
    
    public void setColor(String color) {
        this.color = color;
    }
    
    public long getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }
    
    public boolean isDefault() {
        return isDefault;
    }
    
    public void setDefault(boolean aDefault) {
        isDefault = aDefault;
    }
    
    @Override
    public String toString() {
        return "Category{" +
                "categoryId=" + categoryId +
                ", userId=" + userId +
                ", name='" + name + '\'' +
                ", iconName='" + iconName + '\'' +
                ", color='" + color + '\'' +
                ", createdAt=" + createdAt +
                ", isDefault=" + isDefault +
                '}';
    }
}
