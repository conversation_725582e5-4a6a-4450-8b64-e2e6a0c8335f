package com.example.demo1.repository;

import android.app.Application;

import androidx.lifecycle.LiveData;

import com.example.demo1.database.TodoDatabase;
import com.example.demo1.database.UserDao;
import com.example.demo1.database.CategoryDao;
import com.example.demo1.database.TaskDao;
import com.example.demo1.database.SubTaskDao;
import com.example.demo1.models.User;
import com.example.demo1.models.Category;
import com.example.demo1.models.Task;
import com.example.demo1.models.SubTask;
import com.example.demo1.utils.IconUtils;

import java.util.List;
import java.util.concurrent.Future;

/**
 * Repository class để quản lý tất cả data source cho ứng dụng TodoList
 * Cung cấp API thống nhất cho ViewModel để truy cập dữ liệu
 */
public class TodoRepository {
    
    private UserDao userDao;
    private CategoryDao categoryDao;
    private TaskDao taskDao;
    private SubTaskDao subTaskDao;
    
    /**
     * Constructor khởi tạo repository với application context
     * @param application Application context
     */
    public TodoRepository(Application application) {
        TodoDatabase database = TodoDatabase.getDatabase(application);
        userDao = database.userDao();
        categoryDao = database.categoryDao();
        taskDao = database.taskDao();
        subTaskDao = database.subTaskDao();
    }
    
    // ==================== USER OPERATIONS ====================
    
    /**
     * Thêm user mới
     * @param user User cần thêm
     * @return Future chứa ID của user vừa tạo
     */
    public Future<Long> insertUser(User user) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> {
            long userId = userDao.insertUser(user);
            // Tự động tạo category mặc định "Tất cả" cho user mới
            createDefaultCategoryForUser((int) userId);
            return userId;
        });
    }
    
    /**
     * Cập nhật thông tin user
     * @param user User cần cập nhật
     */
    public void updateUser(User user) {
        TodoDatabase.databaseWriteExecutor.execute(() -> userDao.updateUser(user));
    }
    
    /**
     * Lấy tất cả user đang hoạt động
     * @return LiveData chứa danh sách user
     */
    public LiveData<List<User>> getAllActiveUsers() {
        return userDao.getAllActiveUsers();
    }
    
    /**
     * Lấy user theo ID
     * @param userId ID của user
     * @return LiveData chứa user
     */
    public LiveData<User> getUserById(int userId) {
        return userDao.getUserById(userId);
    }
    
    /**
     * Kiểm tra username đã tồn tại chưa
     * @param username Username cần kiểm tra
     * @return Future chứa kết quả kiểm tra
     */
    public Future<Boolean> checkUsernameExists(String username) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> 
            userDao.checkUsernameExists(username) > 0);
    }
    
    /**
     * Kiểm tra email đã tồn tại chưa
     * @param email Email cần kiểm tra
     * @return Future chứa kết quả kiểm tra
     */
    public Future<Boolean> checkEmailExists(String email) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> 
            userDao.checkEmailExists(email) > 0);
    }
    
    /**
     * Lấy user theo username (cho đăng nhập)
     * @param username Username
     * @return Future chứa user
     */
    public Future<User> getUserByUsername(String username) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> 
            userDao.getUserByUsername(username));
    }
    
    // ==================== CATEGORY OPERATIONS ====================
    
    /**
     * Thêm category mới
     * @param category Category cần thêm
     * @return Future chứa ID của category vừa tạo
     */
    public Future<Long> insertCategory(Category category) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> 
            categoryDao.insertCategory(category));
    }
    
    /**
     * Cập nhật category
     * @param category Category cần cập nhật
     */
    public void updateCategory(Category category) {
        TodoDatabase.databaseWriteExecutor.execute(() -> categoryDao.updateCategory(category));
    }
    
    /**
     * Xóa category
     * @param category Category cần xóa
     */
    public void deleteCategory(Category category) {
        TodoDatabase.databaseWriteExecutor.execute(() -> categoryDao.deleteCategory(category));
    }
    
    /**
     * Lấy tất cả category của user
     * @param userId ID của user
     * @return LiveData chứa danh sách category
     */
    public LiveData<List<Category>> getCategoriesByUserId(int userId) {
        return categoryDao.getCategoriesByUserId(userId);
    }
    
    /**
     * Lấy category theo ID
     * @param categoryId ID của category
     * @return LiveData chứa category
     */
    public LiveData<Category> getCategoryById(int categoryId) {
        return categoryDao.getCategoryById(categoryId);
    }
    
    /**
     * Kiểm tra tên category đã tồn tại chưa
     * @param userId ID của user
     * @param name Tên category
     * @return Future chứa kết quả kiểm tra
     */
    public Future<Boolean> checkCategoryNameExists(int userId, String name) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> 
            categoryDao.checkCategoryNameExists(userId, name) > 0);
    }
    
    /**
     * Tạo category mặc định "Tất cả" cho user mới
     * @param userId ID của user
     */
    private void createDefaultCategoryForUser(int userId) {
        Category defaultCategory = new Category(
            userId, 
            "Tất cả", 
            IconUtils.getDefaultAllCategoryIcon(), 
            IconUtils.getDefaultAllCategoryColor(), 
            true
        );
        categoryDao.insertCategory(defaultCategory);
    }
    
    // ==================== TASK OPERATIONS ====================
    
    /**
     * Thêm task mới
     * @param task Task cần thêm
     * @return Future chứa ID của task vừa tạo
     */
    public Future<Long> insertTask(Task task) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> taskDao.insertTask(task));
    }
    
    /**
     * Cập nhật task
     * @param task Task cần cập nhật
     */
    public void updateTask(Task task) {
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            task.setUpdatedAt(System.currentTimeMillis());
            taskDao.updateTask(task);
        });
    }
    
    /**
     * Xóa task
     * @param task Task cần xóa
     */
    public void deleteTask(Task task) {
        TodoDatabase.databaseWriteExecutor.execute(() -> taskDao.deleteTask(task));
    }
    
    /**
     * Lấy tất cả task của user
     * @param userId ID của user
     * @return LiveData chứa danh sách task
     */
    public LiveData<List<Task>> getTasksByUserId(int userId) {
        return taskDao.getTasksByUserId(userId);
    }
    
    /**
     * Lấy task theo category
     * @param userId ID của user
     * @param categoryId ID của category
     * @return LiveData chứa danh sách task
     */
    public LiveData<List<Task>> getTasksByCategoryId(int userId, int categoryId) {
        return taskDao.getTasksByCategoryId(userId, categoryId);
    }
    
    /**
     * Lấy task theo ID
     * @param taskId ID của task
     * @return LiveData chứa task
     */
    public LiveData<Task> getTaskById(int taskId) {
        return taskDao.getTaskById(taskId);
    }
    
    /**
     * Lấy task đã hoàn thành
     * @param userId ID của user
     * @return LiveData chứa danh sách task đã hoàn thành
     */
    public LiveData<List<Task>> getCompletedTasksByUserId(int userId) {
        return taskDao.getCompletedTasksByUserId(userId);
    }
    
    /**
     * Lấy task chưa hoàn thành
     * @param userId ID của user
     * @return LiveData chứa danh sách task chưa hoàn thành
     */
    public LiveData<List<Task>> getIncompleteTasksByUserId(int userId) {
        return taskDao.getIncompleteTasksByUserId(userId);
    }
    
    /**
     * Lấy task theo khoảng thời gian
     * @param userId ID của user
     * @param startDate Ngày bắt đầu
     * @param endDate Ngày kết thúc
     * @return LiveData chứa danh sách task
     */
    public LiveData<List<Task>> getTasksByDateRange(int userId, long startDate, long endDate) {
        return taskDao.getTasksByDateRange(userId, startDate, endDate);
    }

    // ==================== SUBTASK OPERATIONS ====================

    /**
     * Thêm subtask mới
     * @param subTask SubTask cần thêm
     * @return Future chứa ID của subtask vừa tạo
     */
    public Future<Long> insertSubTask(SubTask subTask) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> subTaskDao.insertSubTask(subTask));
    }

    /**
     * Thêm nhiều subtask cùng lúc
     * @param subTasks Danh sách SubTask cần thêm
     * @return Future chứa danh sách ID
     */
    public Future<List<Long>> insertSubTasks(List<SubTask> subTasks) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> subTaskDao.insertSubTasks(subTasks));
    }

    /**
     * Cập nhật subtask
     * @param subTask SubTask cần cập nhật
     */
    public void updateSubTask(SubTask subTask) {
        TodoDatabase.databaseWriteExecutor.execute(() -> subTaskDao.updateSubTask(subTask));
    }

    /**
     * Xóa subtask
     * @param subTask SubTask cần xóa
     */
    public void deleteSubTask(SubTask subTask) {
        TodoDatabase.databaseWriteExecutor.execute(() -> subTaskDao.deleteSubTask(subTask));
    }

    /**
     * Lấy tất cả subtask của task
     * @param taskId ID của task
     * @return LiveData chứa danh sách subtask
     */
    public LiveData<List<SubTask>> getSubTasksByTaskId(int taskId) {
        return subTaskDao.getSubTasksByTaskId(taskId);
    }

    /**
     * Đánh dấu subtask hoàn thành/chưa hoàn thành
     * @param subTaskId ID của subtask
     * @param isCompleted Trạng thái hoàn thành
     */
    public void updateSubTaskCompletionStatus(int subTaskId, boolean isCompleted) {
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            long completedAt = isCompleted ? System.currentTimeMillis() : 0;
            subTaskDao.updateSubTaskCompletionStatus(subTaskId, isCompleted, completedAt);
        });
    }

    /**
     * Lấy thống kê hoàn thành của task
     * @param taskId ID của task
     * @return Future chứa array [tổng số, số đã hoàn thành]
     */
    public Future<int[]> getTaskCompletionStats(int taskId) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> {
            int total = subTaskDao.getTotalSubTaskCount(taskId);
            int completed = subTaskDao.getCompletedSubTaskCount(taskId);
            return new int[]{total, completed};
        });
    }

    /**
     * Cập nhật trạng thái hoàn thành của task dựa trên subtask
     * @param taskId ID của task
     */
    public void updateTaskCompletionBasedOnSubTasks(int taskId) {
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            int total = subTaskDao.getTotalSubTaskCount(taskId);
            int completed = subTaskDao.getCompletedSubTaskCount(taskId);

            // Task được coi là hoàn thành khi tất cả subtask đều hoàn thành
            boolean isTaskCompleted = (total > 0 && completed == total);

            taskDao.updateTaskCompletionStatus(taskId, isTaskCompleted, System.currentTimeMillis());
        });
    }

    // ==================== UTILITY METHODS ====================

    /**
     * Tính phần trăm hoàn thành của task
     * @param taskId ID của task
     * @return Future chứa phần trăm hoàn thành (0-100)
     */
    public Future<Integer> getTaskCompletionPercentage(int taskId) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> {
            int total = subTaskDao.getTotalSubTaskCount(taskId);
            int completed = subTaskDao.getCompletedSubTaskCount(taskId);

            if (total == 0) return 0;
            return (int) Math.round((double) completed / total * 100);
        });
    }
}
