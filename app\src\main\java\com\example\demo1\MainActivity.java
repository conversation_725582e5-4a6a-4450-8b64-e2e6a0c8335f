package com.example.demo1;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.example.demo1.activities.LoginActivity;
import com.example.demo1.fragments.HomeFragment;
import com.example.demo1.fragments.CalendarFragment;
import com.example.demo1.fragments.ProfileFragment;
import com.example.demo1.viewmodel.UserViewModel;
import com.google.android.material.bottomnavigation.BottomNavigationView;

/**
 * MainActivity - Activity chính của ứng dụng TodoList
 * Quản lý Bottom Navigation và các Fragment chính (Home, Calendar, Profile)
 * <PERSON>ểm tra trạng thái đăng nhập và điều hướng người dùng
 */
public class MainActivity extends AppCompatActivity {

    private static final String TAG = "MainActivity";

    // UI Components
    private BottomNavigationView bottomNavigation;

    // ViewModels
    private UserViewModel userViewModel;

    // Fragments
    private HomeFragment homeFragment;
    private CalendarFragment calendarFragment;
    private ProfileFragment profileFragment;
    private Fragment currentFragment;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate: Khởi tạo MainActivity");

        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);

        // Khởi tạo ViewModels
        initViewModels();

        // Khởi tạo UI
        initViews();

        // Thiết lập observers
        setupObservers();

        // Kiểm tra trạng thái đăng nhập
        checkLoginStatus();

        // Thiết lập window insets
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        Log.d(TAG, "onCreate: Khởi tạo MainActivity thành công");
    }

    /**
     * Khởi tạo ViewModels
     */
    private void initViewModels() {
        Log.d(TAG, "initViewModels: Khởi tạo ViewModels");

        try {
            userViewModel = new ViewModelProvider(this).get(UserViewModel.class);
            Log.d(TAG, "initViewModels: Khởi tạo UserViewModel thành công");
        } catch (Exception e) {
            Log.e(TAG, "initViewModels: Lỗi khi khởi tạo ViewModels", e);
            Toast.makeText(this, "Có lỗi xảy ra khi khởi tạo ứng dụng", Toast.LENGTH_LONG).show();
        }
    }

    /**
     * Khởi tạo Views
     */
    private void initViews() {
        Log.d(TAG, "initViews: Khởi tạo Views");

        try {
            // Khởi tạo Bottom Navigation
            bottomNavigation = findViewById(R.id.bottom_navigation);

            if (bottomNavigation == null) {
                Log.e(TAG, "initViews: Không tìm thấy bottom_navigation trong layout");
                Toast.makeText(this, "Lỗi giao diện: Không tìm thấy bottom navigation", Toast.LENGTH_LONG).show();
                return;
            }

            // Thiết lập Bottom Navigation listener
            bottomNavigation.setOnItemSelectedListener(item -> {
                int itemId = item.getItemId();
                Log.d(TAG, "Bottom Navigation: Item được chọn - ID: " + itemId);

                if (itemId == R.id.nav_home) {
                    showFragment(getHomeFragment());
                    return true;
                } else if (itemId == R.id.nav_calendar) {
                    showFragment(getCalendarFragment());
                    return true;
                } else if (itemId == R.id.nav_profile) {
                    showFragment(getProfileFragment());
                    return true;
                }
                return false;
            });

            // Hiển thị fragment mặc định (Home)
            showFragment(getHomeFragment());

            Log.d(TAG, "initViews: Khởi tạo Views thành công");

        } catch (Exception e) {
            Log.e(TAG, "initViews: Lỗi khi khởi tạo Views", e);
            Toast.makeText(this, "Có lỗi xảy ra khi khởi tạo giao diện", Toast.LENGTH_LONG).show();
        }
    }

    /**
     * Thiết lập observers cho ViewModels
     */
    private void setupObservers() {
        Log.d(TAG, "setupObservers: Thiết lập observers");

        if (userViewModel == null) {
            Log.e(TAG, "setupObservers: UserViewModel null");
            return;
        }

        // Observer cho trạng thái đăng nhập
        userViewModel.getIsLoggedIn().observe(this, isLoggedIn -> {
            Log.d(TAG, "Observer: Trạng thái đăng nhập thay đổi - " + isLoggedIn);

            if (isLoggedIn != null && !isLoggedIn) {
                // Chưa đăng nhập -> chuyển đến LoginActivity
                navigateToLogin();
            } else if (isLoggedIn != null && isLoggedIn) {
                // Đã đăng nhập -> hiển thị UI chính
                showMainUI();
            }
        });

        // Observer cho thông báo lỗi
        userViewModel.getErrorMessage().observe(this, errorMessage -> {
            if (errorMessage != null && !errorMessage.isEmpty()) {
                Log.w(TAG, "Observer: Nhận thông báo lỗi - " + errorMessage);
                Toast.makeText(this, errorMessage, Toast.LENGTH_LONG).show();
                userViewModel.clearErrorMessage();
            }
        });

        Log.d(TAG, "setupObservers: Thiết lập observers thành công");
    }

    /**
     * Kiểm tra trạng thái đăng nhập
     */
    private void checkLoginStatus() {
        Log.d(TAG, "checkLoginStatus: Kiểm tra trạng thái đăng nhập");

        if (userViewModel == null) {
            Log.e(TAG, "checkLoginStatus: UserViewModel null");
            navigateToLogin();
            return;
        }

        boolean isLoggedIn = userViewModel.isUserLoggedIn();
        Log.d(TAG, "checkLoginStatus: Trạng thái đăng nhập hiện tại - " + isLoggedIn);

        if (!isLoggedIn) {
            navigateToLogin();
        } else {
            showMainUI();
        }
    }

    /**
     * Chuyển đến màn hình đăng nhập
     */
    private void navigateToLogin() {
        Log.d(TAG, "navigateToLogin: Chuyển đến màn hình đăng nhập");

        try {
            Intent intent = new Intent(this, LoginActivity.class);
            startActivity(intent);
            finish(); // Đóng MainActivity để không quay lại được

            Log.d(TAG, "navigateToLogin: Chuyển đến LoginActivity thành công");
        } catch (Exception e) {
            Log.e(TAG, "navigateToLogin: Lỗi khi chuyển đến LoginActivity", e);
            Toast.makeText(this, "Có lỗi xảy ra khi chuyển đến màn hình đăng nhập", Toast.LENGTH_LONG).show();
        }
    }

    /**
     * Hiển thị UI chính
     */
    private void showMainUI() {
        Log.d(TAG, "showMainUI: Hiển thị UI chính");

        if (bottomNavigation != null) {
            bottomNavigation.setVisibility(View.VISIBLE);
        }

        // Hiển thị fragment Home nếu chưa có fragment nào
        if (currentFragment == null) {
            showFragment(getHomeFragment());
        }

        Log.d(TAG, "showMainUI: Hiển thị UI chính thành công");
    }

    // ==================== FRAGMENT MANAGEMENT ====================

    /**
     * Lấy HomeFragment (tạo mới nếu chưa có)
     * @return HomeFragment instance
     */
    private HomeFragment getHomeFragment() {
        if (homeFragment == null) {
            Log.d(TAG, "getHomeFragment: Tạo HomeFragment mới");
            homeFragment = new HomeFragment();
        }
        return homeFragment;
    }

    /**
     * Lấy CalendarFragment (tạo mới nếu chưa có)
     * @return CalendarFragment instance
     */
    private CalendarFragment getCalendarFragment() {
        if (calendarFragment == null) {
            Log.d(TAG, "getCalendarFragment: Tạo CalendarFragment mới");
            calendarFragment = new CalendarFragment();
        }
        return calendarFragment;
    }

    /**
     * Lấy ProfileFragment (tạo mới nếu chưa có)
     * @return ProfileFragment instance
     */
    private ProfileFragment getProfileFragment() {
        if (profileFragment == null) {
            Log.d(TAG, "getProfileFragment: Tạo ProfileFragment mới");
            profileFragment = new ProfileFragment();
        }
        return profileFragment;
    }

    /**
     * Hiển thị fragment
     * @param fragment Fragment cần hiển thị
     */
    private void showFragment(Fragment fragment) {
        Log.d(TAG, "showFragment: Hiển thị fragment - " + fragment.getClass().getSimpleName());

        if (fragment == null) {
            Log.e(TAG, "showFragment: Fragment null");
            return;
        }

        try {
            // Kiểm tra xem fragment đã được add chưa
            if (!fragment.isAdded()) {
                getSupportFragmentManager()
                    .beginTransaction()
                    .add(R.id.fragment_container, fragment)
                    .commit();
                Log.d(TAG, "showFragment: Add fragment mới");
            }

            // Ẩn fragment hiện tại
            if (currentFragment != null && currentFragment != fragment) {
                getSupportFragmentManager()
                    .beginTransaction()
                    .hide(currentFragment)
                    .show(fragment)
                    .commit();
                Log.d(TAG, "showFragment: Chuyển từ " + currentFragment.getClass().getSimpleName() +
                    " sang " + fragment.getClass().getSimpleName());
            } else if (currentFragment == null) {
                getSupportFragmentManager()
                    .beginTransaction()
                    .show(fragment)
                    .commit();
                Log.d(TAG, "showFragment: Hiển thị fragment đầu tiên");
            }

            currentFragment = fragment;
            Log.d(TAG, "showFragment: Hiển thị fragment thành công");

        } catch (Exception e) {
            Log.e(TAG, "showFragment: Lỗi khi hiển thị fragment", e);
            Toast.makeText(this, "Có lỗi xảy ra khi chuyển màn hình", Toast.LENGTH_SHORT).show();
        }
    }

    // ==================== LIFECYCLE METHODS ====================

    @Override
    protected void onResume() {
        super.onResume();
        Log.d(TAG, "onResume: MainActivity resume");

        // Kiểm tra lại trạng thái đăng nhập khi quay lại
        checkLoginStatus();
    }

    @Override
    protected void onPause() {
        super.onPause();
        Log.d(TAG, "onPause: MainActivity pause");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "onDestroy: MainActivity destroy");

        // Cleanup resources
        homeFragment = null;
        calendarFragment = null;
        profileFragment = null;
        currentFragment = null;
    }

    @Override
    public void onBackPressed() {
        Log.d(TAG, "onBackPressed: Người dùng nhấn nút back");

        // Nếu đang ở Home fragment thì thoát ứng dụng
        if (currentFragment instanceof HomeFragment) {
            Log.d(TAG, "onBackPressed: Đang ở Home fragment, thoát ứng dụng");
            super.onBackPressed();
        } else {
            // Chuyển về Home fragment
            Log.d(TAG, "onBackPressed: Chuyển về Home fragment");
            bottomNavigation.setSelectedItemId(R.id.nav_home);
            showFragment(getHomeFragment());
        }
    }

    // ==================== PUBLIC METHODS ====================

    /**
     * Lấy UserViewModel (cho các Fragment sử dụng)
     * @return UserViewModel instance
     */
    public UserViewModel getUserViewModel() {
        return userViewModel;
    }

    /**
     * Hiển thị thông báo Toast
     * @param message Nội dung thông báo
     */
    public void showToast(String message) {
        Log.d(TAG, "showToast: " + message);
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    /**
     * Hiển thị thông báo Toast dài
     * @param message Nội dung thông báo
     */
    public void showLongToast(String message) {
        Log.d(TAG, "showLongToast: " + message);
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }
}