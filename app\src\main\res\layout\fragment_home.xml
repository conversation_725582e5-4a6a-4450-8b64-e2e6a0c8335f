<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".fragments.HomeFragment">

    <!-- Header -->
    <TextView
        android:id="@+id/tv_header"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Công việc của tôi"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:padding="16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Categories RecyclerView -->
    <TextView
        android:id="@+id/tv_categories_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Chủ đề"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_header"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_categories"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:paddingHorizontal="16dp"
        android:clipToPadding="false"
        app:layout_constraintTop_toBottomOf="@+id/tv_categories_label"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:listitem="@layout/item_category" />

    <!-- Filter Chips -->
    <com.google.android.material.chip.ChipGroup
        android:id="@+id/chip_group_filter"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:paddingHorizontal="16dp"
        app:singleSelection="true"
        app:layout_constraintTop_toBottomOf="@+id/rv_categories"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_all"
            style="@style/Widget.MaterialComponents.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Tất cả"
            android:checked="true" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_incomplete"
            style="@style/Widget.MaterialComponents.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Chưa hoàn thành" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_completed"
            style="@style/Widget.MaterialComponents.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Đã hoàn thành" />

    </com.google.android.material.chip.ChipGroup>

    <!-- Tasks RecyclerView -->
    <TextView
        android:id="@+id/tv_tasks_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Công việc"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@+id/chip_group_filter"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_tasks"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:paddingHorizontal="16dp"
        android:paddingBottom="16dp"
        android:clipToPadding="false"
        app:layout_constraintTop_toBottomOf="@+id/tv_tasks_label"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:listitem="@layout/item_task" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/layout_empty_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@+id/rv_tasks"
        app:layout_constraintBottom_toBottomOf="@+id/rv_tasks"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📝"
            android:textSize="48sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Chưa có công việc nào"
            android:textSize="16sp"
            android:textColor="@color/text_secondary"
            android:layout_marginTop="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Nhấn nút + để thêm công việc mới"
            android:textSize="14sp"
            android:textColor="@color/text_hint"
            android:layout_marginTop="4dp" />

    </LinearLayout>

    <!-- Floating Action Buttons -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_category"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="88dp"
        android:src="@drawable/ic_add_category"
        android:contentDescription="Thêm chủ đề"
        app:backgroundTint="@color/accent"
        app:tint="@color/white"
        app:fabSize="mini"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_task"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:src="@drawable/ic_add"
        android:contentDescription="Thêm công việc"
        app:backgroundTint="@color/primary"
        app:tint="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
