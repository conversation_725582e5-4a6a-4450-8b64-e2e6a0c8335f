{"logs": [{"outputFile": "com.example.demo1.app-mergeDebugResources-46:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f96c501bd1ba1d6317adff2fbcd632aa\\transformed\\material-1.10.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,327,406,486,568,670,764,860,986,1067,1133,1225,1302,1365,1473,1533,1599,1655,1726,1786,1840,1959,2016,2078,2132,2207,2331,2419,2502,2647,2732,2818,2951,3039,3117,3171,3225,3291,3365,3443,3530,3612,3684,3761,3834,3904,4013,4106,4178,4270,4366,4440,4516,4612,4665,4747,4814,4901,4988,5050,5114,5177,5246,5354,5459,5560,5663,5721,5779", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,78,79,81,101,93,95,125,80,65,91,76,62,107,59,65,55,70,59,53,118,56,61,53,74,123,87,82,144,84,85,132,87,77,53,53,65,73,77,86,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,104,100,102,57,57,79", "endOffsets": "322,401,481,563,665,759,855,981,1062,1128,1220,1297,1360,1468,1528,1594,1650,1721,1781,1835,1954,2011,2073,2127,2202,2326,2414,2497,2642,2727,2813,2946,3034,3112,3166,3220,3286,3360,3438,3525,3607,3679,3756,3829,3899,4008,4101,4173,4265,4361,4435,4511,4607,4660,4742,4809,4896,4983,5045,5109,5172,5241,5349,5454,5555,5658,5716,5774,5854"}, "to": {"startLines": "2,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3102,3181,3261,3343,3445,3539,3635,3761,3842,3908,4000,4077,4140,4248,4308,4374,4430,4501,4561,4615,4734,4791,4853,4907,4982,5106,5194,5277,5422,5507,5593,5726,5814,5892,5946,6000,6066,6140,6218,6305,6387,6459,6536,6609,6679,6788,6881,6953,7045,7141,7215,7291,7387,7440,7522,7589,7676,7763,7825,7889,7952,8021,8129,8234,8335,8438,8496,8782", "endLines": "6,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,103", "endColumns": "12,78,79,81,101,93,95,125,80,65,91,76,62,107,59,65,55,70,59,53,118,56,61,53,74,123,87,82,144,84,85,132,87,77,53,53,65,73,77,86,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,104,100,102,57,57,79", "endOffsets": "372,3176,3256,3338,3440,3534,3630,3756,3837,3903,3995,4072,4135,4243,4303,4369,4425,4496,4556,4610,4729,4786,4848,4902,4977,5101,5189,5272,5417,5502,5588,5721,5809,5887,5941,5995,6061,6135,6213,6300,6382,6454,6531,6604,6674,6783,6876,6948,7040,7136,7210,7286,7382,7435,7517,7584,7671,7758,7820,7884,7947,8016,8124,8229,8330,8433,8491,8549,8857"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\306de9d3f8c563e7b276242941a9a359\\transformed\\appcompat-1.6.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2243,2348,2462,2565,2734,2830", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2238,2343,2457,2560,2729,2825,2912"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "377,498,595,702,788,892,1014,1099,1181,1272,1365,1460,1554,1654,1747,1842,1937,2028,2119,2207,2310,2414,2515,2620,2734,2837,3006,8862", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "493,590,697,783,887,1009,1094,1176,1267,1360,1455,1549,1649,1742,1837,1932,2023,2114,2202,2305,2409,2510,2615,2729,2832,3001,3097,8944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2da51fb8d78929212dd6b1464ada4ac4\\transformed\\core-1.9.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "105", "startColumns": "4", "startOffsets": "8949", "endColumns": "100", "endOffsets": "9045"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2781b657211557e5dc71f94e7a48b4d1\\transformed\\navigation-ui-2.7.6\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,158", "endColumns": "102,124", "endOffsets": "153,278"}, "to": {"startLines": "101,102", "startColumns": "4,4", "startOffsets": "8554,8657", "endColumns": "102,124", "endOffsets": "8652,8777"}}]}]}