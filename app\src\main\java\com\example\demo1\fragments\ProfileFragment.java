package com.example.demo1.fragments;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.example.demo1.MainActivity;
import com.example.demo1.R;
import com.example.demo1.activities.LoginActivity;
import com.example.demo1.models.User;
import com.example.demo1.viewmodel.CategoryViewModel;
import com.example.demo1.viewmodel.TaskViewModel;
import com.example.demo1.viewmodel.UserViewModel;

/**
 * ProfileFragment - Fragment hiển thị thông tin cá nhân và thống kê
 * Chứa thông tin user, thống kê task, category và chức năng đăng xuất
 */
public class ProfileFragment extends Fragment {
    
    private static final String TAG = "ProfileFragment";
    
    // UI Components
    private TextView tvUsername, tvEmail, tvFullName, tvJoinDate;
    private TextView tvTotalTasks, tvCompletedTasks, tvIncompleteTasks;
    private TextView tvTotalCategories, tvCompletionRate;
    private Button btnLogout, btnEditProfile;
    
    // ViewModels
    private UserViewModel userViewModel;
    private CategoryViewModel categoryViewModel;
    private TaskViewModel taskViewModel;
    
    // Data
    private User currentUser;
    private int currentUserId = -1;
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate: Khởi tạo ProfileFragment");
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, 
                           @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "onCreateView: Tạo view cho ProfileFragment");
        
        View view = inflater.inflate(R.layout.fragment_profile, container, false);
        
        // Khởi tạo ViewModels
        initViewModels();
        
        // Khởi tạo UI
        initViews(view);
        
        // Thiết lập listeners
        setupListeners();
        
        // Thiết lập observers
        setupObservers();
        
        // Load dữ liệu ban đầu
        loadInitialData();
        
        Log.d(TAG, "onCreateView: Tạo view thành công");
        return view;
    }
    
    /**
     * Khởi tạo ViewModels
     */
    private void initViewModels() {
        Log.d(TAG, "initViewModels: Khởi tạo ViewModels");
        
        if (getActivity() == null) {
            Log.e(TAG, "initViewModels: Activity null");
            return;
        }
        
        try {
            userViewModel = new ViewModelProvider(requireActivity()).get(UserViewModel.class);
            categoryViewModel = new ViewModelProvider(requireActivity()).get(CategoryViewModel.class);
            taskViewModel = new ViewModelProvider(requireActivity()).get(TaskViewModel.class);
            
            Log.d(TAG, "initViewModels: Khởi tạo ViewModels thành công");
        } catch (Exception e) {
            Log.e(TAG, "initViewModels: Lỗi khi khởi tạo ViewModels", e);
            showToast("Có lỗi xảy ra khi khởi tạo");
        }
    }
    
    /**
     * Khởi tạo Views
     * @param view Root view
     */
    private void initViews(View view) {
        Log.d(TAG, "initViews: Khởi tạo Views");
        
        try {
            // Thông tin user
            tvUsername = view.findViewById(R.id.tv_username);
            tvEmail = view.findViewById(R.id.tv_email);
            tvFullName = view.findViewById(R.id.tv_full_name);
            tvJoinDate = view.findViewById(R.id.tv_join_date);
            
            // Thống kê
            tvTotalTasks = view.findViewById(R.id.tv_total_tasks);
            tvCompletedTasks = view.findViewById(R.id.tv_completed_tasks);
            tvIncompleteTasks = view.findViewById(R.id.tv_incomplete_tasks);
            tvTotalCategories = view.findViewById(R.id.tv_total_categories);
            tvCompletionRate = view.findViewById(R.id.tv_completion_rate);
            
            // Buttons
            btnLogout = view.findViewById(R.id.btn_logout);
            btnEditProfile = view.findViewById(R.id.btn_edit_profile);
            
            // Kiểm tra các view bắt buộc
            if (tvUsername == null || btnLogout == null) {
                Log.e(TAG, "initViews: Một số view bắt buộc không tìm thấy");
                showToast("Lỗi giao diện: Thiếu các thành phần cần thiết");
                return;
            }
            
            Log.d(TAG, "initViews: Khởi tạo Views thành công");
            
        } catch (Exception e) {
            Log.e(TAG, "initViews: Lỗi khi khởi tạo Views", e);
            showToast("Có lỗi xảy ra khi khởi tạo giao diện");
        }
    }
    
    /**
     * Thiết lập listeners
     */
    private void setupListeners() {
        Log.d(TAG, "setupListeners: Thiết lập listeners");
        
        // Nút đăng xuất
        if (btnLogout != null) {
            btnLogout.setOnClickListener(v -> {
                Log.d(TAG, "btnLogout: Người dùng nhấn đăng xuất");
                showLogoutConfirmDialog();
            });
        }
        
        // Nút sửa profile
        if (btnEditProfile != null) {
            btnEditProfile.setOnClickListener(v -> {
                Log.d(TAG, "btnEditProfile: Người dùng nhấn sửa profile");
                showEditProfileDialog();
            });
        }
        
        Log.d(TAG, "setupListeners: Thiết lập listeners thành công");
    }
    
    /**
     * Thiết lập observers
     */
    private void setupObservers() {
        Log.d(TAG, "setupObservers: Thiết lập observers");
        
        if (userViewModel == null || categoryViewModel == null || taskViewModel == null) {
            Log.e(TAG, "setupObservers: Một số ViewModel null");
            return;
        }
        
        // Observer cho user hiện tại
        userViewModel.getCurrentUser().observe(getViewLifecycleOwner(), user -> {
            if (user != null) {
                currentUser = user;
                currentUserId = user.getUserId();
                Log.d(TAG, "Observer: Current user changed - " + user.getUsername());
                
                // Hiển thị thông tin user
                displayUserInfo(user);
                
                // Khởi tạo ViewModels với userId mới
                categoryViewModel.initWithUserId(currentUserId);
                taskViewModel.initWithUserId(currentUserId);
                
                // Load thống kê
                loadStatistics();
            }
        });
        
        // Observer cho tổng số task
        taskViewModel.getTasks().observe(getViewLifecycleOwner(), tasks -> {
            if (tasks != null && tvTotalTasks != null) {
                tvTotalTasks.setText(String.valueOf(tasks.size()));
                Log.d(TAG, "Observer: Total tasks = " + tasks.size());
            }
        });
        
        // Observer cho task đã hoàn thành
        taskViewModel.getCompletedTasks().observe(getViewLifecycleOwner(), tasks -> {
            if (tasks != null && tvCompletedTasks != null) {
                tvCompletedTasks.setText(String.valueOf(tasks.size()));
                Log.d(TAG, "Observer: Completed tasks = " + tasks.size());
                
                // Cập nhật tỷ lệ hoàn thành
                updateCompletionRate();
            }
        });
        
        // Observer cho task chưa hoàn thành
        taskViewModel.getIncompleteTasks().observe(getViewLifecycleOwner(), tasks -> {
            if (tasks != null && tvIncompleteTasks != null) {
                tvIncompleteTasks.setText(String.valueOf(tasks.size()));
                Log.d(TAG, "Observer: Incomplete tasks = " + tasks.size());
                
                // Cập nhật tỷ lệ hoàn thành
                updateCompletionRate();
            }
        });
        
        // Observer cho categories
        categoryViewModel.getCategories().observe(getViewLifecycleOwner(), categories -> {
            if (categories != null && tvTotalCategories != null) {
                // Trừ đi 1 cho category mặc định "Tất cả"
                int normalCategoryCount = Math.max(0, categories.size() - 1);
                tvTotalCategories.setText(String.valueOf(normalCategoryCount));
                Log.d(TAG, "Observer: Total categories = " + normalCategoryCount);
            }
        });
        
        // Observer cho trạng thái đăng nhập
        userViewModel.getIsLoggedIn().observe(getViewLifecycleOwner(), isLoggedIn -> {
            if (isLoggedIn != null && !isLoggedIn) {
                Log.d(TAG, "Observer: User đã đăng xuất");
                navigateToLogin();
            }
        });
        
        // Observer cho error messages
        userViewModel.getErrorMessage().observe(getViewLifecycleOwner(), message -> {
            if (message != null && !message.isEmpty()) {
                Log.w(TAG, "Observer: User error - " + message);
                showToast(message);
                userViewModel.clearErrorMessage();
            }
        });
        
        Log.d(TAG, "setupObservers: Thiết lập observers thành công");
    }
    
    /**
     * Load dữ liệu ban đầu
     */
    private void loadInitialData() {
        Log.d(TAG, "loadInitialData: Load dữ liệu ban đầu");
        
        if (userViewModel == null) {
            Log.e(TAG, "loadInitialData: UserViewModel null");
            return;
        }
        
        currentUserId = userViewModel.getCurrentUserId();
        Log.d(TAG, "loadInitialData: Current userId = " + currentUserId);
        
        if (currentUserId > 0) {
            // Load thông tin user hiện tại
            userViewModel.getCurrentUser().observe(getViewLifecycleOwner(), user -> {
                if (user != null) {
                    currentUser = user;
                    displayUserInfo(user);
                }
            });
        } else {
            Log.w(TAG, "loadInitialData: User chưa đăng nhập");
        }
    }
    
    /**
     * Hiển thị thông tin user
     * @param user User cần hiển thị
     */
    private void displayUserInfo(User user) {
        Log.d(TAG, "displayUserInfo: Hiển thị thông tin user - " + user.getUsername());
        
        try {
            if (tvUsername != null) {
                tvUsername.setText(user.getUsername());
            }
            
            if (tvEmail != null) {
                tvEmail.setText(user.getEmail());
            }
            
            if (tvFullName != null) {
                tvFullName.setText(user.getFullName());
            }
            
            if (tvJoinDate != null) {
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("dd/MM/yyyy", 
                    java.util.Locale.getDefault());
                String joinDate = sdf.format(new java.util.Date(user.getCreatedAt()));
                tvJoinDate.setText("Tham gia: " + joinDate);
            }
            
            Log.d(TAG, "displayUserInfo: Hiển thị thông tin thành công");
            
        } catch (Exception e) {
            Log.e(TAG, "displayUserInfo: Lỗi khi hiển thị thông tin", e);
        }
    }
    
    /**
     * Load thống kê
     */
    private void loadStatistics() {
        Log.d(TAG, "loadStatistics: Load thống kê");
        
        // Thống kê sẽ được load thông qua observers
        // Không cần thực hiện gì thêm ở đây
    }
    
    /**
     * Cập nhật tỷ lệ hoàn thành
     */
    private void updateCompletionRate() {
        Log.d(TAG, "updateCompletionRate: Cập nhật tỷ lệ hoàn thành");
        
        if (tvCompletionRate == null || tvTotalTasks == null || tvCompletedTasks == null) {
            return;
        }
        
        try {
            String totalText = tvTotalTasks.getText().toString();
            String completedText = tvCompletedTasks.getText().toString();
            
            int total = Integer.parseInt(totalText);
            int completed = Integer.parseInt(completedText);
            
            if (total == 0) {
                tvCompletionRate.setText("0%");
            } else {
                int rate = (int) Math.round((double) completed / total * 100);
                tvCompletionRate.setText(rate + "%");
            }
            
            Log.d(TAG, "updateCompletionRate: " + completed + "/" + total + 
                " = " + tvCompletionRate.getText());
            
        } catch (NumberFormatException e) {
            Log.e(TAG, "updateCompletionRate: Lỗi khi tính tỷ lệ", e);
            tvCompletionRate.setText("0%");
        }
    }
    
    /**
     * Hiển thị dialog xác nhận đăng xuất
     */
    private void showLogoutConfirmDialog() {
        Log.d(TAG, "showLogoutConfirmDialog: Hiển thị dialog xác nhận đăng xuất");
        
        if (getContext() == null) {
            Log.e(TAG, "showLogoutConfirmDialog: Context null");
            return;
        }
        
        try {
            new androidx.appcompat.app.AlertDialog.Builder(getContext())
                .setTitle("Xác nhận đăng xuất")
                .setMessage("Bạn có chắc chắn muốn đăng xuất?")
                .setPositiveButton("Đăng xuất", (dialog, which) -> {
                    Log.d(TAG, "showLogoutConfirmDialog: Người dùng xác nhận đăng xuất");
                    performLogout();
                })
                .setNegativeButton("Hủy", (dialog, which) -> {
                    Log.d(TAG, "showLogoutConfirmDialog: Người dùng hủy đăng xuất");
                    dialog.dismiss();
                })
                .show();
                
        } catch (Exception e) {
            Log.e(TAG, "showLogoutConfirmDialog: Lỗi khi hiển thị dialog", e);
            showToast("Có lỗi xảy ra khi hiển thị dialog");
        }
    }
    
    /**
     * Thực hiện đăng xuất
     */
    private void performLogout() {
        Log.d(TAG, "performLogout: Thực hiện đăng xuất");
        
        if (userViewModel != null) {
            userViewModel.logoutUser();
        }
    }
    
    /**
     * Hiển thị dialog sửa profile
     */
    private void showEditProfileDialog() {
        Log.d(TAG, "showEditProfileDialog: Hiển thị dialog sửa profile");
        
        // TODO: Implement EditProfileDialog
        showToast("Chức năng sửa thông tin cá nhân sẽ được cập nhật trong phiên bản tiếp theo");
    }
    
    /**
     * Chuyển đến màn hình đăng nhập
     */
    private void navigateToLogin() {
        Log.d(TAG, "navigateToLogin: Chuyển đến màn hình đăng nhập");
        
        if (getContext() == null) {
            Log.e(TAG, "navigateToLogin: Context null");
            return;
        }
        
        try {
            Intent intent = new Intent(getContext(), LoginActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            startActivity(intent);
            
            if (getActivity() != null) {
                getActivity().finish();
            }
            
            Log.d(TAG, "navigateToLogin: Chuyển đến LoginActivity thành công");
        } catch (Exception e) {
            Log.e(TAG, "navigateToLogin: Lỗi khi chuyển đến LoginActivity", e);
            showToast("Có lỗi xảy ra khi chuyển màn hình");
        }
    }
    
    // ==================== UTILITY METHODS ====================
    
    /**
     * Hiển thị Toast message
     * @param message Nội dung message
     */
    private void showToast(String message) {
        Log.d(TAG, "showToast: " + message);
        
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        } else if (getActivity() instanceof MainActivity) {
            ((MainActivity) getActivity()).showToast(message);
        }
    }
    
    /**
     * Refresh dữ liệu
     */
    public void refreshData() {
        Log.d(TAG, "refreshData: Refresh dữ liệu");
        
        loadStatistics();
    }
    
    // ==================== LIFECYCLE METHODS ====================
    
    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "onResume: ProfileFragment resume");
        
        // Refresh dữ liệu khi quay lại fragment
        refreshData();
    }
    
    @Override
    public void onPause() {
        super.onPause();
        Log.d(TAG, "onPause: ProfileFragment pause");
    }
    
    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d(TAG, "onDestroyView: ProfileFragment destroy view");
        
        // Cleanup
        currentUser = null;
    }
}
