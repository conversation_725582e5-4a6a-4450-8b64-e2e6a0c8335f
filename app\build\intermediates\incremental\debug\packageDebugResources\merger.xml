<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res"><file name="ic_launcher_background" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="activity_main" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="accent">#FF5722</color><color name="gray_50">#FAFAFA</color><color name="primary">#2196F3</color><color name="accent_light">#FFCCBC</color><color name="background">#FAFAFA</color><color name="text_secondary">#757575</color><color name="priority_low">#4CAF50</color><color name="error">#F44336</color><color name="text_primary">#212121</color><color name="success">#4CAF50</color><color name="primary_dark">#1976D2</color><color name="surface">#FFFFFF</color><color name="gray_900">#212121</color><color name="gray_800">#424242</color><color name="gray_700">#616161</color><color name="gray_600">#757575</color><color name="text_hint">#BDBDBD</color><color name="gray_500">#9E9E9E</color><color name="gray_400">#BDBDBD</color><color name="primary_light">#BBDEFB</color><color name="warning">#FF9800</color><color name="gray_300">#E0E0E0</color><color name="priority_high">#F44336</color><color name="gray_200">#EEEEEE</color><color name="gray_100">#F5F5F5</color><color name="info">#2196F3</color><color name="priority_medium">#FF9800</color></file><file path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Demo1</string></file><file path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.Demo1" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.Demo1" parent="Base.Theme.Demo1"/></file><file path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.Demo1" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="bottom_nav_color" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\color\bottom_nav_color.xml" qualifiers="" type="color"/><file name="bg_priority_high" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\drawable\bg_priority_high.xml" qualifiers="" type="drawable"/><file name="bg_priority_low" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\drawable\bg_priority_low.xml" qualifiers="" type="drawable"/><file name="bg_priority_medium" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\drawable\bg_priority_medium.xml" qualifiers="" type="drawable"/><file name="ic_add" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_add_category" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\drawable\ic_add_category.xml" qualifiers="" type="drawable"/><file name="ic_calendar" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\drawable\ic_calendar.xml" qualifiers="" type="drawable"/><file name="ic_calendar_small" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\drawable\ic_calendar_small.xml" qualifiers="" type="drawable"/><file name="ic_description" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\drawable\ic_description.xml" qualifiers="" type="drawable"/><file name="ic_edit" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\drawable\ic_edit.xml" qualifiers="" type="drawable"/><file name="ic_email" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\drawable\ic_email.xml" qualifiers="" type="drawable"/><file name="ic_home" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\drawable\ic_home.xml" qualifiers="" type="drawable"/><file name="ic_logout" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\drawable\ic_logout.xml" qualifiers="" type="drawable"/><file name="ic_person" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\drawable\ic_person.xml" qualifiers="" type="drawable"/><file name="ic_profile" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\drawable\ic_profile.xml" qualifiers="" type="drawable"/><file name="ic_task" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\drawable\ic_task.xml" qualifiers="" type="drawable"/><file name="activity_add_edit_task" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\layout\activity_add_edit_task.xml" qualifiers="" type="layout"/><file name="activity_login" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="fragment_calendar" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\layout\fragment_calendar.xml" qualifiers="" type="layout"/><file name="fragment_home" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\layout\fragment_home.xml" qualifiers="" type="layout"/><file name="fragment_profile" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\layout\fragment_profile.xml" qualifiers="" type="layout"/><file name="item_category" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\layout\item_category.xml" qualifiers="" type="layout"/><file name="item_task" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\layout\item_task.xml" qualifiers="" type="layout"/><file name="bottom_navigation_menu" path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\res\menu\bottom_navigation_menu.xml" qualifiers="" type="menu"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>