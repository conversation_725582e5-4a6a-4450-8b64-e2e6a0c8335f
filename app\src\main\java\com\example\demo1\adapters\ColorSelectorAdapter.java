package com.example.demo1.adapters;

import android.graphics.Color;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.card.MaterialCardView;

import com.example.demo1.R;

import java.util.List;

/**
 * ColorSelectorAdapter - Adapter cho việc chọn màu trong dialog
 * Hiển thị danh sách màu dưới dạng grid với khả năng chọn
 */
public class ColorSelectorAdapter extends RecyclerView.Adapter<ColorSelectorAdapter.ColorViewHolder> {
    
    private static final String TAG = "ColorSelectorAdapter";
    
    // Interface cho color selection
    public interface OnColorSelectedListener {
        void onColorSelected(String colorHex);
    }
    
    // Data và listener
    private List<String> colors;
    private OnColorSelectedListener listener;
    private int selectedPosition = -1;
    private String selectedColor = null;
    
    /**
     * Constructor
     * @param colors Danh sách mã màu hex
     * @param listener Listener cho color selection
     */
    public ColorSelectorAdapter(List<String> colors, OnColorSelectedListener listener) {
        this.colors = colors;
        this.listener = listener;
        Log.d(TAG, "ColorSelectorAdapter: Khởi tạo với " + 
            (colors != null ? colors.size() : 0) + " colors");
    }
    
    @NonNull
    @Override
    public ColorViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        Log.d(TAG, "onCreateViewHolder: Tạo ViewHolder mới");
        
        View view = LayoutInflater.from(parent.getContext())
            .inflate(R.layout.item_color_selector, parent, false);
        return new ColorViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ColorViewHolder holder, int position) {
        if (colors == null || position < 0 || position >= colors.size()) {
            Log.e(TAG, "onBindViewHolder: Position không hợp lệ - " + position);
            return;
        }
        
        String colorHex = colors.get(position);
        Log.d(TAG, "onBindViewHolder: Bind color - " + colorHex + " tại position " + position);
        
        holder.bind(colorHex, position == selectedPosition);
    }
    
    @Override
    public int getItemCount() {
        int count = colors != null ? colors.size() : 0;
        Log.d(TAG, "getItemCount: " + count);
        return count;
    }
    
    /**
     * Lấy màu được chọn
     * @return Mã màu hex được chọn hoặc null
     */
    public String getSelectedColor() {
        Log.d(TAG, "getSelectedColor: " + selectedColor);
        return selectedColor;
    }
    
    /**
     * ViewHolder cho Color item
     */
    class ColorViewHolder extends RecyclerView.ViewHolder {

        private MaterialCardView cardView;
        private View colorView;
        
        public ColorViewHolder(@NonNull View itemView) {
            super(itemView);
            Log.d(TAG, "ColorViewHolder: Khởi tạo ViewHolder");
            
            // Tìm các view
            cardView = itemView.findViewById(R.id.card_color);
            colorView = itemView.findViewById(R.id.view_color);
            
            // Kiểm tra các view bắt buộc
            if (cardView == null || colorView == null) {
                Log.e(TAG, "ColorViewHolder: Một số view bắt buộc không tìm thấy");
            }
            
            // Thiết lập click listener
            itemView.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && colors != null && listener != null) {
                    String colorHex = colors.get(position);
                    Log.d(TAG, "ColorViewHolder: Click vào color - " + colorHex);
                    
                    // Cập nhật selected position
                    int oldPosition = selectedPosition;
                    selectedPosition = position;
                    selectedColor = colorHex;
                    
                    // Cập nhật UI
                    notifyItemChanged(oldPosition);
                    notifyItemChanged(selectedPosition);
                    
                    // Gọi listener
                    listener.onColorSelected(colorHex);
                }
            });
        }
        
        /**
         * Bind dữ liệu màu vào view
         * @param colorHex Mã màu hex
         * @param isSelected Màu có được chọn không
         */
        public void bind(String colorHex, boolean isSelected) {
            Log.d(TAG, "ColorViewHolder.bind: Bind color - " + colorHex + ", selected: " + isSelected);
            
            try {
                // Thiết lập màu
                if (colorView != null) {
                    int color = Color.parseColor(colorHex);
                    colorView.setBackgroundColor(color);
                }
                
                // Thiết lập trạng thái selected
                if (cardView != null) {
                    if (isSelected) {
                        cardView.setCardElevation(8f);
                        cardView.setStrokeWidth(4);
                        cardView.setStrokeColor(Color.BLACK);
                    } else {
                        cardView.setCardElevation(2f);
                        cardView.setStrokeWidth(0);
                    }
                }
                
                Log.d(TAG, "ColorViewHolder.bind: Bind thành công");
                
            } catch (Exception e) {
                Log.e(TAG, "ColorViewHolder.bind: Lỗi khi bind dữ liệu", e);
                
                // Thiết lập màu mặc định nếu có lỗi
                if (colorView != null) {
                    colorView.setBackgroundColor(Color.GRAY);
                }
            }
        }
    }
}
