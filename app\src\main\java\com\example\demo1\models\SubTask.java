package com.example.demo1.models;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;
import androidx.room.ForeignKey;

/**
 * Entity SubTask - Đại diện cho công việc con trong một Task
 * Mỗi SubTask thuộc về một Task và có thể được đánh dấu hoàn thành/chưa hoàn thành
 */
@Entity(tableName = "subtasks",
        foreignKeys = @ForeignKey(
                entity = Task.class,
                parentColumns = "task_id",
                childColumns = "task_id",
                onDelete = ForeignKey.CASCADE
        ))
public class SubTask {
    
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "subtask_id")
    private int subTaskId;
    
    @ColumnInfo(name = "task_id")
    private int taskId; // Liên kết với Task
    
    @ColumnInfo(name = "title")
    private String title;
    
    @ColumnInfo(name = "description")
    private String description;
    
    @ColumnInfo(name = "is_completed")
    private boolean isCompleted;
    
    @ColumnInfo(name = "created_at")
    private long createdAt;
    
    @ColumnInfo(name = "completed_at")
    private long completedAt; // Thời gian hoàn thành (0 nếu chưa hoàn thành)
    
    @ColumnInfo(name = "order_index")
    private int orderIndex; // Thứ tự hiển thị trong danh sách
    
    // Constructor mặc định
    public SubTask() {
        this.createdAt = System.currentTimeMillis();
        this.isCompleted = false;
        this.completedAt = 0;
        this.orderIndex = 0;
    }
    
    // Constructor với tham số
    public SubTask(int taskId, String title, String description, int orderIndex) {
        this.taskId = taskId;
        this.title = title;
        this.description = description;
        this.orderIndex = orderIndex;
        this.createdAt = System.currentTimeMillis();
        this.isCompleted = false;
        this.completedAt = 0;
    }
    
    // Phương thức để đánh dấu hoàn thành
    public void markAsCompleted() {
        this.isCompleted = true;
        this.completedAt = System.currentTimeMillis();
    }
    
    // Phương thức để bỏ đánh dấu hoàn thành
    public void markAsIncomplete() {
        this.isCompleted = false;
        this.completedAt = 0;
    }
    
    // Getter và Setter methods
    public int getSubTaskId() {
        return subTaskId;
    }
    
    public void setSubTaskId(int subTaskId) {
        this.subTaskId = subTaskId;
    }
    
    public int getTaskId() {
        return taskId;
    }
    
    public void setTaskId(int taskId) {
        this.taskId = taskId;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public boolean isCompleted() {
        return isCompleted;
    }
    
    public void setCompleted(boolean completed) {
        this.isCompleted = completed;
        if (completed && this.completedAt == 0) {
            this.completedAt = System.currentTimeMillis();
        } else if (!completed) {
            this.completedAt = 0;
        }
    }
    
    public long getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }
    
    public long getCompletedAt() {
        return completedAt;
    }
    
    public void setCompletedAt(long completedAt) {
        this.completedAt = completedAt;
    }
    
    public int getOrderIndex() {
        return orderIndex;
    }
    
    public void setOrderIndex(int orderIndex) {
        this.orderIndex = orderIndex;
    }
    
    @Override
    public String toString() {
        return "SubTask{" +
                "subTaskId=" + subTaskId +
                ", taskId=" + taskId +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", isCompleted=" + isCompleted +
                ", createdAt=" + createdAt +
                ", completedAt=" + completedAt +
                ", orderIndex=" + orderIndex +
                '}';
    }
}
