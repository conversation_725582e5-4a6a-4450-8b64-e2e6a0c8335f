package com.example.demo1.activities;

import android.app.DatePickerDialog;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.ViewModelProvider;

import com.example.demo1.R;
import com.example.demo1.models.Category;
import com.example.demo1.models.Task;
import com.example.demo1.viewmodel.CategoryViewModel;
import com.example.demo1.viewmodel.TaskViewModel;
import com.example.demo1.viewmodel.UserViewModel;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * AddEditTaskActivity - Màn hình thêm mới hoặc chỉnh sửa Task
 * Cho phép người dùng tạo task mới hoặc cập nhật task hiện có
 * Bao gồm chức năng thêm/xóa subtask động
 */
public class AddEditTaskActivity extends AppCompatActivity {
    
    private static final String TAG = "AddEditTaskActivity";
    
    // Intent extras
    public static final String EXTRA_TASK_ID = "task_id";
    public static final String EXTRA_CATEGORY_ID = "category_id";
    
    // UI Components
    private EditText etTitle, etDescription;
    private AutoCompleteTextView spinnerCategory, spinnerPriority;
    private TextView tvDueDate;
    private Button btnSelectDate, btnSave, btnCancel;
    private LinearLayout layoutSubTasks;
    private FloatingActionButton fabAddSubTask;
    private ProgressBar progressBar;
    
    // ViewModels
    private UserViewModel userViewModel;
    private CategoryViewModel categoryViewModel;
    private TaskViewModel taskViewModel;
    
    // Data
    private List<Category> categories = new ArrayList<>();
    private List<EditText> subTaskEditTexts = new ArrayList<>();
    private long selectedDueDate = 0;
    private int taskId = -1; // -1 = thêm mới, > 0 = chỉnh sửa
    private int preSelectedCategoryId = -1;
    private Task currentTask = null;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate: Khởi tạo AddEditTaskActivity");
        
        setContentView(R.layout.activity_add_edit_task);
        
        // Lấy dữ liệu từ Intent
        getIntentData();
        
        // Khởi tạo ViewModels
        initViewModels();
        
        // Khởi tạo UI
        initViews();
        
        // Thiết lập listeners
        setupListeners();
        
        // Thiết lập observers
        setupObservers();
        
        // Load dữ liệu ban đầu
        loadInitialData();
        
        Log.d(TAG, "onCreate: Khởi tạo AddEditTaskActivity thành công");
    }
    
    /**
     * Lấy dữ liệu từ Intent
     */
    private void getIntentData() {
        Log.d(TAG, "getIntentData: Lấy dữ liệu từ Intent");
        
        Intent intent = getIntent();
        if (intent != null) {
            taskId = intent.getIntExtra(EXTRA_TASK_ID, -1);
            preSelectedCategoryId = intent.getIntExtra(EXTRA_CATEGORY_ID, -1);
            
            Log.d(TAG, "getIntentData: taskId = " + taskId + ", preSelectedCategoryId = " + preSelectedCategoryId);
        }
    }
    
    /**
     * Khởi tạo ViewModels
     */
    private void initViewModels() {
        Log.d(TAG, "initViewModels: Khởi tạo ViewModels");
        
        try {
            userViewModel = new ViewModelProvider(this).get(UserViewModel.class);
            categoryViewModel = new ViewModelProvider(this).get(CategoryViewModel.class);
            taskViewModel = new ViewModelProvider(this).get(TaskViewModel.class);
            
            Log.d(TAG, "initViewModels: Khởi tạo ViewModels thành công");
        } catch (Exception e) {
            Log.e(TAG, "initViewModels: Lỗi khi khởi tạo ViewModels", e);
            Toast.makeText(this, "Có lỗi xảy ra khi khởi tạo ứng dụng", Toast.LENGTH_LONG).show();
        }
    }
    
    /**
     * Khởi tạo Views
     */
    private void initViews() {
        Log.d(TAG, "initViews: Khởi tạo Views");
        
        try {
            // Tìm các view
            etTitle = findViewById(R.id.et_title);
            etDescription = findViewById(R.id.et_description);
            spinnerCategory = findViewById(R.id.spinner_category);
            spinnerPriority = findViewById(R.id.spinner_priority);
            tvDueDate = findViewById(R.id.tv_due_date);
            btnSelectDate = findViewById(R.id.btn_select_date);
            btnSave = findViewById(R.id.btn_save);
            btnCancel = findViewById(R.id.btn_cancel);
            layoutSubTasks = findViewById(R.id.layout_subtasks);
            fabAddSubTask = findViewById(R.id.fab_add_subtask);
            progressBar = findViewById(R.id.progress_bar);
            
            // Kiểm tra các view bắt buộc
            if (etTitle == null || spinnerCategory == null || spinnerPriority == null || 
                btnSave == null || layoutSubTasks == null || fabAddSubTask == null) {
                Log.e(TAG, "initViews: Một số view bắt buộc không tìm thấy trong layout");
                Toast.makeText(this, "Lỗi giao diện: Thiếu các thành phần cần thiết", Toast.LENGTH_LONG).show();
                return;
            }
            
            // Thiết lập Spinner Priority
            setupPrioritySpinner();
            
            // Thiết lập title
            setTitle(taskId == -1 ? "Thêm Công Việc" : "Sửa Công Việc");
            
            Log.d(TAG, "initViews: Khởi tạo Views thành công");
            
        } catch (Exception e) {
            Log.e(TAG, "initViews: Lỗi khi khởi tạo Views", e);
            Toast.makeText(this, "Có lỗi xảy ra khi khởi tạo giao diện", Toast.LENGTH_LONG).show();
        }
    }
    
    /**
     * Thiết lập Spinner Priority
     */
    private void setupPrioritySpinner() {
        Log.d(TAG, "setupPrioritySpinner: Thiết lập Spinner Priority");

        if (spinnerPriority == null || taskViewModel == null) {
            Log.e(TAG, "setupPrioritySpinner: Thiếu components cần thiết");
            return;
        }

        try {
            List<String> priorities = taskViewModel.getPriorityLevels();
            ArrayAdapter<String> adapter = new ArrayAdapter<>(this,
                android.R.layout.simple_dropdown_item_1line, priorities);
            spinnerPriority.setAdapter(adapter);

            // Mặc định chọn "Trung bình"
            if (priorities.size() > 1) {
                spinnerPriority.setText(priorities.get(1), false);
            }

            Log.d(TAG, "setupPrioritySpinner: Thiết lập thành công với " + priorities.size() + " mức độ");
        } catch (Exception e) {
            Log.e(TAG, "setupPrioritySpinner: Lỗi khi thiết lập", e);
        }
    }
    
    /**
     * Thiết lập listeners
     */
    private void setupListeners() {
        Log.d(TAG, "setupListeners: Thiết lập listeners");
        
        // Nút chọn ngày
        if (btnSelectDate != null) {
            btnSelectDate.setOnClickListener(v -> showDatePicker());
        }
        
        // Nút thêm subtask
        if (fabAddSubTask != null) {
            fabAddSubTask.setOnClickListener(v -> addSubTaskInput());
        }
        
        // Nút lưu
        if (btnSave != null) {
            btnSave.setOnClickListener(v -> saveTask());
        }
        
        // Nút hủy
        if (btnCancel != null) {
            btnCancel.setOnClickListener(v -> {
                Log.d(TAG, "btnCancel: Người dùng hủy");
                finish();
            });
        }
        
        Log.d(TAG, "setupListeners: Thiết lập listeners thành công");
    }
    
    /**
     * Thiết lập observers
     */
    private void setupObservers() {
        Log.d(TAG, "setupObservers: Thiết lập observers");
        
        if (userViewModel == null || categoryViewModel == null || taskViewModel == null) {
            Log.e(TAG, "setupObservers: Một số ViewModel null");
            return;
        }
        
        // Observer cho categories
        categoryViewModel.getCategories().observe(this, categoryList -> {
            Log.d(TAG, "Observer: Nhận được " + 
                (categoryList != null ? categoryList.size() : 0) + " categories");
            
            if (categoryList != null) {
                categories.clear();
                categories.addAll(categoryList);
                setupCategorySpinner();
            }
        });
        
        // Observer cho loading states
        taskViewModel.getIsLoading().observe(this, isLoading -> {
            if (progressBar != null) {
                progressBar.setVisibility(isLoading != null && isLoading ? View.VISIBLE : View.GONE);
            }
            setButtonsEnabled(isLoading == null || !isLoading);
        });
        
        // Observer cho success message
        taskViewModel.getSuccessMessage().observe(this, message -> {
            if (message != null && !message.isEmpty()) {
                Log.i(TAG, "Observer: Thành công - " + message);
                Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
                taskViewModel.clearSuccessMessage();
                
                // Đóng activity sau khi lưu thành công
                finish();
            }
        });
        
        // Observer cho error message
        taskViewModel.getErrorMessage().observe(this, message -> {
            if (message != null && !message.isEmpty()) {
                Log.w(TAG, "Observer: Lỗi - " + message);
                Toast.makeText(this, message, Toast.LENGTH_LONG).show();
                taskViewModel.clearErrorMessage();
            }
        });
        
        // Observer cho task được chọn (khi edit)
        if (taskId > 0) {
            taskViewModel.getSelectedTask().observe(this, task -> {
                if (task != null && task.getTaskId() == taskId) {
                    Log.d(TAG, "Observer: Nhận được task để edit - " + task.getTitle());
                    currentTask = task;
                    populateTaskData(task);
                }
            });
        }
        
        Log.d(TAG, "setupObservers: Thiết lập observers thành công");
    }
    
    /**
     * Load dữ liệu ban đầu
     */
    private void loadInitialData() {
        Log.d(TAG, "loadInitialData: Load dữ liệu ban đầu");

        if (userViewModel == null || categoryViewModel == null || taskViewModel == null) {
            Log.e(TAG, "loadInitialData: Một số ViewModel null");
            return;
        }

        int userId = userViewModel.getCurrentUserId();
        Log.d(TAG, "loadInitialData: Current userId = " + userId);

        if (userId <= 0) {
            Log.e(TAG, "loadInitialData: User chưa đăng nhập");
            Toast.makeText(this, "Vui lòng đăng nhập lại", Toast.LENGTH_LONG).show();
            finish();
            return;
        }

        // Khởi tạo ViewModels với userId
        categoryViewModel.initWithUserId(userId);
        taskViewModel.initWithUserId(userId);

        // Nếu là chế độ edit, load task data
        if (taskId > 0) {
            Log.d(TAG, "loadInitialData: Load task để edit - taskId: " + taskId);
            // Task sẽ được load thông qua observer
        } else {
            // Thêm một subtask input mặc định
            addSubTaskInput();
        }
    }

    /**
     * Thiết lập Category Spinner
     */
    private void setupCategorySpinner() {
        Log.d(TAG, "setupCategorySpinner: Thiết lập Category Spinner với " + categories.size() + " categories");

        if (spinnerCategory == null || categories.isEmpty()) {
            Log.w(TAG, "setupCategorySpinner: Spinner null hoặc categories trống");
            return;
        }

        try {
            // Tạo danh sách tên category
            List<String> categoryNames = new ArrayList<>();
            for (Category category : categories) {
                categoryNames.add(category.getName());
            }

            ArrayAdapter<String> adapter = new ArrayAdapter<>(this,
                android.R.layout.simple_dropdown_item_1line, categoryNames);
            spinnerCategory.setAdapter(adapter);

            // Chọn category được chỉ định trước (nếu có)
            if (preSelectedCategoryId > 0) {
                for (int i = 0; i < categories.size(); i++) {
                    if (categories.get(i).getCategoryId() == preSelectedCategoryId) {
                        spinnerCategory.setText(categories.get(i).getName(), false);
                        Log.d(TAG, "setupCategorySpinner: Chọn category: " + categories.get(i).getName());
                        break;
                    }
                }
            } else if (!categoryNames.isEmpty()) {
                // Chọn category đầu tiên nếu không có pre-selected
                spinnerCategory.setText(categoryNames.get(0), false);
            }

            Log.d(TAG, "setupCategorySpinner: Thiết lập thành công");
        } catch (Exception e) {
            Log.e(TAG, "setupCategorySpinner: Lỗi khi thiết lập", e);
        }
    }

    /**
     * Hiển thị DatePicker để chọn ngày hết hạn
     */
    private void showDatePicker() {
        Log.d(TAG, "showDatePicker: Hiển thị DatePicker");

        Calendar calendar = Calendar.getInstance();
        if (selectedDueDate > 0) {
            calendar.setTimeInMillis(selectedDueDate);
        }

        DatePickerDialog datePickerDialog = new DatePickerDialog(
            this,
            (view, year, month, dayOfMonth) -> {
                Calendar selectedCalendar = Calendar.getInstance();
                selectedCalendar.set(year, month, dayOfMonth);
                selectedDueDate = selectedCalendar.getTimeInMillis();

                // Hiển thị ngày đã chọn
                SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
                String dateString = sdf.format(new Date(selectedDueDate));

                if (tvDueDate != null) {
                    tvDueDate.setText("Hết hạn: " + dateString);
                }

                Log.d(TAG, "showDatePicker: Đã chọn ngày - " + dateString);
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        );

        // Không cho chọn ngày trong quá khứ
        datePickerDialog.getDatePicker().setMinDate(System.currentTimeMillis());
        datePickerDialog.show();
    }

    /**
     * Thêm input field cho subtask
     */
    private void addSubTaskInput() {
        Log.d(TAG, "addSubTaskInput: Thêm input field cho subtask");

        if (layoutSubTasks == null) {
            Log.e(TAG, "addSubTaskInput: layoutSubTasks null");
            return;
        }

        try {
            // Tạo EditText mới cho subtask
            EditText etSubTask = new EditText(this);
            etSubTask.setHint("Nhập công việc con...");
            etSubTask.setLayoutParams(new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ));

            // Thêm margin
            LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) etSubTask.getLayoutParams();
            params.setMargins(0, 8, 0, 8);
            etSubTask.setLayoutParams(params);

            // Thêm vào layout và danh sách
            layoutSubTasks.addView(etSubTask);
            subTaskEditTexts.add(etSubTask);

            Log.d(TAG, "addSubTaskInput: Đã thêm subtask input. Tổng: " + subTaskEditTexts.size());

        } catch (Exception e) {
            Log.e(TAG, "addSubTaskInput: Lỗi khi thêm subtask input", e);
        }
    }

    /**
     * Populate dữ liệu task khi edit
     * @param task Task cần edit
     */
    private void populateTaskData(Task task) {
        Log.d(TAG, "populateTaskData: Populate dữ liệu task - " + task.getTitle());

        try {
            // Điền thông tin cơ bản
            if (etTitle != null) etTitle.setText(task.getTitle());
            if (etDescription != null) etDescription.setText(task.getDescription());

            // Chọn category
            if (spinnerCategory != null && !categories.isEmpty()) {
                for (Category category : categories) {
                    if (category.getCategoryId() == task.getCategoryId()) {
                        spinnerCategory.setText(category.getName(), false);
                        break;
                    }
                }
            }

            // Chọn priority
            if (spinnerPriority != null) {
                spinnerPriority.setText(task.getPriority(), false);
            }

            // Hiển thị ngày hết hạn
            if (task.getDueDate() > 0) {
                selectedDueDate = task.getDueDate();
                SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
                String dateString = sdf.format(new Date(selectedDueDate));
                if (tvDueDate != null) {
                    tvDueDate.setText("Hết hạn: " + dateString);
                }
            }

            // Load subtasks
            taskViewModel.loadSubTasksForTask(task.getTaskId());

            Log.d(TAG, "populateTaskData: Populate thành công");

        } catch (Exception e) {
            Log.e(TAG, "populateTaskData: Lỗi khi populate dữ liệu", e);
        }
    }

    /**
     * Lưu task
     */
    private void saveTask() {
        Log.d(TAG, "saveTask: Bắt đầu lưu task");

        if (!validateInput()) {
            Log.w(TAG, "saveTask: Validation failed");
            return;
        }

        try {
            // Lấy dữ liệu từ form
            String title = etTitle.getText().toString().trim();
            String description = etDescription != null ? etDescription.getText().toString().trim() : "";

            // Lấy category được chọn
            String selectedCategoryName = spinnerCategory.getText().toString().trim();
            Category selectedCategory = null;
            for (Category category : categories) {
                if (category.getName().equals(selectedCategoryName)) {
                    selectedCategory = category;
                    break;
                }
            }

            if (selectedCategory == null) {
                Toast.makeText(this, "Vui lòng chọn chủ đề hợp lệ", Toast.LENGTH_SHORT).show();
                return;
            }

            // Lấy priority được chọn
            String priority = spinnerPriority.getText().toString().trim();
            if (priority.isEmpty()) {
                priority = "Trung bình"; // Mặc định
            }

            // Lấy danh sách subtask
            List<String> subTaskTitles = new ArrayList<>();
            for (EditText etSubTask : subTaskEditTexts) {
                String subTaskTitle = etSubTask.getText().toString().trim();
                if (!subTaskTitle.isEmpty()) {
                    subTaskTitles.add(subTaskTitle);
                }
            }

            Log.d(TAG, "saveTask: Dữ liệu - title: " + title + ", category: " + selectedCategory.getName() +
                ", priority: " + priority + ", subtasks: " + subTaskTitles.size());

            if (taskId == -1) {
                // Thêm task mới
                taskViewModel.addTask(title, description, selectedCategory.getCategoryId(),
                    priority, selectedDueDate, subTaskTitles);
            } else {
                // Cập nhật task hiện có
                if (currentTask != null) {
                    taskViewModel.updateTask(currentTask, title, description,
                        selectedCategory.getCategoryId(), priority, selectedDueDate);
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "saveTask: Lỗi khi lưu task", e);
            Toast.makeText(this, "Có lỗi xảy ra khi lưu: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    /**
     * Validate input
     * @return true nếu hợp lệ, false nếu không
     */
    private boolean validateInput() {
        Log.d(TAG, "validateInput: Validate input");

        if (etTitle == null || etTitle.getText().toString().trim().isEmpty()) {
            Toast.makeText(this, "Vui lòng nhập tiêu đề", Toast.LENGTH_SHORT).show();
            if (etTitle != null) etTitle.requestFocus();
            return false;
        }

        if (spinnerCategory == null || categories.isEmpty()) {
            Toast.makeText(this, "Không có chủ đề nào. Vui lòng tạo chủ đề trước", Toast.LENGTH_LONG).show();
            return false;
        }

        Log.d(TAG, "validateInput: Validation thành công");
        return true;
    }

    /**
     * Enable/Disable buttons
     * @param enabled true để enable, false để disable
     */
    private void setButtonsEnabled(boolean enabled) {
        if (btnSave != null) btnSave.setEnabled(enabled);
        if (btnCancel != null) btnCancel.setEnabled(enabled);
        if (btnSelectDate != null) btnSelectDate.setEnabled(enabled);
        if (fabAddSubTask != null) fabAddSubTask.setEnabled(enabled);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "onDestroy: AddEditTaskActivity destroy");

        // Cleanup
        subTaskEditTexts.clear();
        categories.clear();
    }
}
