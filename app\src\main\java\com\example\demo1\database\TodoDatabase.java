package com.example.demo1.database;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.example.demo1.models.User;
import com.example.demo1.models.Category;
import com.example.demo1.models.Task;
import com.example.demo1.models.SubTask;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Room Database class cho ứng dụng TodoList
 * Quản lý tất cả các Entity và DAO, cung cấp singleton instance
 */
@Database(
        entities = {User.class, Category.class, Task.class, SubTask.class},
        version = 1,
        exportSchema = false
)
public abstract class TodoDatabase extends RoomDatabase {
    
    // Tên database
    private static final String DATABASE_NAME = "todo_database";
    
    // Singleton instance
    private static volatile TodoDatabase INSTANCE;
    
    // Thread pool để thực hiện các database operations
    private static final int NUMBER_OF_THREADS = 4;
    public static final ExecutorService databaseWriteExecutor = 
            Executors.newFixedThreadPool(NUMBER_OF_THREADS);
    
    // Abstract methods để lấy các DAO
    public abstract UserDao userDao();
    public abstract CategoryDao categoryDao();
    public abstract TaskDao taskDao();
    public abstract SubTaskDao subTaskDao();
    
    /**
     * Lấy singleton instance của database
     * @param context Application context
     * @return Instance của TodoDatabase
     */
    public static TodoDatabase getDatabase(final Context context) {
        if (INSTANCE == null) {
            synchronized (TodoDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(
                            context.getApplicationContext(),
                            TodoDatabase.class,
                            DATABASE_NAME
                    )
                    .addCallback(roomDatabaseCallback) // Thêm callback để khởi tạo dữ liệu
                    .build();
                }
            }
        }
        return INSTANCE;
    }
    
    /**
     * Callback để khởi tạo dữ liệu mặc định khi database được tạo lần đầu
     */
    private static RoomDatabase.Callback roomDatabaseCallback = new RoomDatabase.Callback() {
        @Override
        public void onCreate(SupportSQLiteDatabase db) {
            super.onCreate(db);
            // Khởi tạo dữ liệu mặc định trong background thread
            databaseWriteExecutor.execute(() -> {
                // Có thể thêm dữ liệu mặc định ở đây nếu cần
                populateInitialData(INSTANCE);
            });
        }
    };
    
    /**
     * Khởi tạo dữ liệu mặc định cho database
     * @param database Instance của database
     */
    private static void populateInitialData(TodoDatabase database) {
        UserDao userDao = database.userDao();
        CategoryDao categoryDao = database.categoryDao();
        
        // Kiểm tra xem đã có dữ liệu chưa
        // Nếu chưa có user nào thì tạo user demo
        // (Trong thực tế, việc này sẽ được xử lý ở màn hình đăng ký)
        
        // Ví dụ tạo user demo (có thể bỏ qua nếu không cần)
        /*
        User demoUser = new User("demo", "<EMAIL>", "Demo User");
        long userId = userDao.insertUser(demoUser);
        
        // Tạo category mặc định "Tất cả"
        Category allCategory = new Category((int)userId, "Tất cả", "category", "#2196F3", true);
        categoryDao.insertCategory(allCategory);
        */
    }
    
    /**
     * Đóng database và cleanup resources
     */
    public static void closeDatabase() {
        if (INSTANCE != null) {
            INSTANCE.close();
            INSTANCE = null;
        }
        if (!databaseWriteExecutor.isShutdown()) {
            databaseWriteExecutor.shutdown();
        }
    }
    
    /**
     * Migration từ version 1 lên version 2 (ví dụ cho tương lai)
     */
    static final Migration MIGRATION_1_2 = new Migration(1, 2) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            // Thực hiện migration khi cần thiết
            // Ví dụ: database.execSQL("ALTER TABLE users ADD COLUMN avatar_url TEXT");
        }
    };
}
