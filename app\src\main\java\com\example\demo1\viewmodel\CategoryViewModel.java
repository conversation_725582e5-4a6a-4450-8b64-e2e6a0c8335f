package com.example.demo1.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.example.demo1.models.Category;
import com.example.demo1.repository.TodoRepository;
import com.example.demo1.utils.IconUtils;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * ViewModel cho Category - Quản lý UI state và business logic liên quan đến Category
 * Chứa các chức năng CRUD category, validation, và quản lý trạng thái UI
 */
public class CategoryViewModel extends AndroidViewModel {
    
    private static final String TAG = "CategoryViewModel";
    
    private TodoRepository repository;
    
    // LiveData cho danh sách category
    private MutableLiveData<List<Category>> categories;
    
    // LiveData cho category được chọn
    private MutableLiveData<Category> selectedCategory;
    
    // LiveData cho trạng thái loading
    private MutableLiveData<Boolean> isLoading;
    
    // LiveData cho thông báo lỗi
    private MutableLiveData<String> errorMessage;
    
    // LiveData cho thông báo thành công
    private MutableLiveData<String> successMessage;
    
    // User ID hiện tại
    private int currentUserId = -1;
    
    /**
     * Constructor khởi tạo ViewModel
     * @param application Application context
     */
    public CategoryViewModel(@NonNull Application application) {
        super(application);
        Log.d(TAG, "CategoryViewModel: Khởi tạo CategoryViewModel");
        
        // Khởi tạo repository
        repository = new TodoRepository(application);
        
        // Khởi tạo LiveData
        categories = new MutableLiveData<>();
        selectedCategory = new MutableLiveData<>();
        isLoading = new MutableLiveData<>(false);
        errorMessage = new MutableLiveData<>();
        successMessage = new MutableLiveData<>();
        
        Log.d(TAG, "CategoryViewModel: Khởi tạo thành công tất cả LiveData");
    }
    
    // ==================== GETTER METHODS ====================
    
    public LiveData<List<Category>> getCategories() {
        Log.d(TAG, "getCategories: Lấy danh sách category cho user: " + currentUserId);
        return categories;
    }
    
    public LiveData<Category> getSelectedCategory() {
        return selectedCategory;
    }
    
    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }
    
    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }
    
    public LiveData<String> getSuccessMessage() {
        return successMessage;
    }
    
    // ==================== INITIALIZATION ====================
    
    /**
     * Khởi tạo ViewModel với user ID
     * @param userId ID của user hiện tại
     */
    public void initWithUserId(int userId) {
        Log.d(TAG, "initWithUserId: Khởi tạo với userId: " + userId);
        
        if (userId <= 0) {
            Log.e(TAG, "initWithUserId: UserId không hợp lệ: " + userId);
            errorMessage.setValue("User ID không hợp lệ");
            return;
        }
        
        this.currentUserId = userId;
        loadCategoriesForUser(userId);
        
        Log.d(TAG, "initWithUserId: Khởi tạo thành công cho user: " + userId);
    }
    
    /**
     * Load danh sách category cho user
     * @param userId ID của user
     */
    private void loadCategoriesForUser(int userId) {
        Log.d(TAG, "loadCategoriesForUser: Load category cho user: " + userId);
        
        LiveData<List<Category>> categoriesLiveData = repository.getCategoriesByUserId(userId);
        categoriesLiveData.observeForever(categoryList -> {
            Log.d(TAG, "loadCategoriesForUser: Nhận được " + 
                (categoryList != null ? categoryList.size() : 0) + " categories");
            
            categories.setValue(categoryList);
            
            // Tự động chọn category đầu tiên (thường là "Tất cả")
            if (categoryList != null && !categoryList.isEmpty() && selectedCategory.getValue() == null) {
                Category firstCategory = categoryList.get(0);
                selectedCategory.setValue(firstCategory);
                Log.d(TAG, "loadCategoriesForUser: Tự động chọn category đầu tiên: " + firstCategory.getName());
            }
        });
    }
    
    // ==================== CATEGORY CRUD OPERATIONS ====================
    
    /**
     * Thêm category mới
     * @param name Tên category
     * @param iconName Tên icon
     * @param color Mã màu
     */
    public void addCategory(String name, String iconName, String color) {
        Log.d(TAG, "addCategory: Thêm category mới - name: " + name + ", icon: " + iconName + ", color: " + color);
        
        if (!validateCategoryInput(name, iconName, color)) {
            Log.e(TAG, "addCategory: Validation failed");
            return;
        }
        
        if (currentUserId <= 0) {
            errorMessage.setValue("Chưa đăng nhập");
            Log.e(TAG, "addCategory: User chưa đăng nhập");
            return;
        }
        
        isLoading.setValue(true);
        
        try {
            // Kiểm tra tên category đã tồn tại chưa
            boolean nameExists = repository.checkCategoryNameExists(currentUserId, name.trim()).get();
            Log.d(TAG, "addCategory: Kiểm tra tên tồn tại - kết quả: " + nameExists);
            
            if (nameExists) {
                errorMessage.setValue("Tên chủ đề đã tồn tại");
                Log.w(TAG, "addCategory: Tên category đã tồn tại");
                return;
            }
            
            // Tạo category mới
            Category newCategory = new Category(currentUserId, name.trim(), iconName, color);
            Log.d(TAG, "addCategory: Tạo category mới - " + newCategory.toString());
            
            long categoryId = repository.insertCategory(newCategory).get();
            Log.d(TAG, "addCategory: Thêm category thành công - categoryId: " + categoryId);
            
            successMessage.setValue("Thêm chủ đề thành công!");
            Log.i(TAG, "addCategory: Thêm category thành công: " + name);
            
        } catch (ExecutionException | InterruptedException e) {
            Log.e(TAG, "addCategory: Lỗi khi thêm category", e);
            errorMessage.setValue("Có lỗi xảy ra khi thêm chủ đề: " + e.getMessage());
        } finally {
            isLoading.setValue(false);
        }
    }
    
    /**
     * Cập nhật category
     * @param category Category cần cập nhật
     * @param newName Tên mới
     * @param newIconName Icon mới
     * @param newColor Màu mới
     */
    public void updateCategory(Category category, String newName, String newIconName, String newColor) {
        Log.d(TAG, "updateCategory: Cập nhật category - id: " + category.getCategoryId() + 
            ", newName: " + newName + ", newIcon: " + newIconName + ", newColor: " + newColor);
        
        if (!validateCategoryInput(newName, newIconName, newColor)) {
            Log.e(TAG, "updateCategory: Validation failed");
            return;
        }
        
        if (category.isDefault()) {
            errorMessage.setValue("Không thể sửa chủ đề mặc định");
            Log.w(TAG, "updateCategory: Không thể sửa category mặc định");
            return;
        }
        
        isLoading.setValue(true);
        
        try {
            // Kiểm tra tên mới đã tồn tại chưa (trừ category hiện tại)
            // TODO: Cần thêm method checkCategoryNameExistsExclude vào repository
            
            // Cập nhật thông tin category
            category.setName(newName.trim());
            category.setIconName(newIconName);
            category.setColor(newColor);
            
            Log.d(TAG, "updateCategory: Cập nhật category - " + category.toString());
            
            repository.updateCategory(category);
            
            successMessage.setValue("Cập nhật chủ đề thành công!");
            Log.i(TAG, "updateCategory: Cập nhật category thành công: " + newName);
            
        } catch (Exception e) {
            Log.e(TAG, "updateCategory: Lỗi khi cập nhật category", e);
            errorMessage.setValue("Có lỗi xảy ra khi cập nhật chủ đề: " + e.getMessage());
        } finally {
            isLoading.setValue(false);
        }
    }
    
    /**
     * Xóa category
     * @param category Category cần xóa
     */
    public void deleteCategory(Category category) {
        Log.d(TAG, "deleteCategory: Xóa category - id: " + category.getCategoryId() + ", name: " + category.getName());
        
        if (category.isDefault()) {
            errorMessage.setValue("Không thể xóa chủ đề mặc định");
            Log.w(TAG, "deleteCategory: Không thể xóa category mặc định");
            return;
        }
        
        isLoading.setValue(true);
        
        try {
            repository.deleteCategory(category);
            
            // Nếu category đang được chọn thì chọn category khác
            if (selectedCategory.getValue() != null && 
                selectedCategory.getValue().getCategoryId() == category.getCategoryId()) {
                
                List<Category> currentCategories = categories.getValue();
                if (currentCategories != null && !currentCategories.isEmpty()) {
                    // Chọn category đầu tiên (thường là "Tất cả")
                    selectedCategory.setValue(currentCategories.get(0));
                    Log.d(TAG, "deleteCategory: Chuyển sang category: " + currentCategories.get(0).getName());
                }
            }
            
            successMessage.setValue("Xóa chủ đề thành công!");
            Log.i(TAG, "deleteCategory: Xóa category thành công: " + category.getName());
            
        } catch (Exception e) {
            Log.e(TAG, "deleteCategory: Lỗi khi xóa category", e);
            errorMessage.setValue("Có lỗi xảy ra khi xóa chủ đề: " + e.getMessage());
        } finally {
            isLoading.setValue(false);
        }
    }
    
    // ==================== CATEGORY SELECTION ====================
    
    /**
     * Chọn category
     * @param category Category được chọn
     */
    public void selectCategory(Category category) {
        Log.d(TAG, "selectCategory: Chọn category - id: " + category.getCategoryId() + ", name: " + category.getName());
        selectedCategory.setValue(category);
    }
    
    /**
     * Lấy ID của category được chọn
     * @return ID của category được chọn, -1 nếu chưa chọn
     */
    public int getSelectedCategoryId() {
        Category category = selectedCategory.getValue();
        int categoryId = category != null ? category.getCategoryId() : -1;
        Log.d(TAG, "getSelectedCategoryId: " + categoryId);
        return categoryId;
    }
    
    // ==================== VALIDATION METHODS ====================
    
    /**
     * Validate dữ liệu category
     * @param name Tên category
     * @param iconName Tên icon
     * @param color Mã màu
     * @return true nếu hợp lệ, false nếu không
     */
    private boolean validateCategoryInput(String name, String iconName, String color) {
        Log.d(TAG, "validateCategoryInput: Validate input - name: " + name + ", icon: " + iconName + ", color: " + color);
        
        if (name == null || name.trim().isEmpty()) {
            errorMessage.setValue("Vui lòng nhập tên chủ đề");
            Log.w(TAG, "validateCategoryInput: Tên trống");
            return false;
        }
        
        if (name.trim().length() < 2) {
            errorMessage.setValue("Tên chủ đề phải có ít nhất 2 ký tự");
            Log.w(TAG, "validateCategoryInput: Tên quá ngắn");
            return false;
        }
        
        if (!IconUtils.isValidIcon(iconName)) {
            errorMessage.setValue("Icon không hợp lệ");
            Log.w(TAG, "validateCategoryInput: Icon không hợp lệ: " + iconName);
            return false;
        }
        
        if (!IconUtils.isValidColor(color)) {
            errorMessage.setValue("Màu sắc không hợp lệ");
            Log.w(TAG, "validateCategoryInput: Màu không hợp lệ: " + color);
            return false;
        }
        
        Log.d(TAG, "validateCategoryInput: Validation thành công");
        return true;
    }
    
    // ==================== UTILITY METHODS ====================
    
    /**
     * Clear error message
     */
    public void clearErrorMessage() {
        Log.d(TAG, "clearErrorMessage: Xóa thông báo lỗi");
        errorMessage.setValue(null);
    }
    
    /**
     * Clear success message
     */
    public void clearSuccessMessage() {
        Log.d(TAG, "clearSuccessMessage: Xóa thông báo thành công");
        successMessage.setValue(null);
    }
    
    /**
     * Lấy danh sách icon có sẵn
     * @return Danh sách tên icon
     */
    public List<String> getAvailableIcons() {
        Log.d(TAG, "getAvailableIcons: Lấy danh sách icon có sẵn");
        return IconUtils.AVAILABLE_ICONS;
    }
    
    /**
     * Lấy danh sách màu có sẵn
     * @return Danh sách mã màu
     */
    public List<String> getAvailableColors() {
        Log.d(TAG, "getAvailableColors: Lấy danh sách màu có sẵn");
        return IconUtils.AVAILABLE_COLORS;
    }
}
