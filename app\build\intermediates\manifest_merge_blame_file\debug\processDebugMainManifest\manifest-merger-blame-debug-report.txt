1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.demo1"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="35" />
10
11    <permission
11-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.demo1.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.demo1.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:5:5-24:19
18        android:allowBackup="true"
18-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:12:9-35
28        android:testOnly="true"
29        android:theme="@style/Theme.Demo1" >
29-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:13:9-43
30        <activity
30-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:15:9-23:20
31            android:name="com.example.demo1.MainActivity"
31-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:16:13-41
32            android:exported="true" >
32-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:17:13-36
33            <intent-filter>
33-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:18:13-22:29
34                <action android:name="android.intent.action.MAIN" />
34-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:19:17-69
34-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:19:25-66
35
36                <category android:name="android.intent.category.LAUNCHER" />
36-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:21:17-77
36-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:21:27-74
37            </intent-filter>
38        </activity>
39
40        <service
40-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd38f0899b054cfe98a372c15c899d74\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
41            android:name="androidx.room.MultiInstanceInvalidationService"
41-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd38f0899b054cfe98a372c15c899d74\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
42            android:directBootAware="true"
42-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd38f0899b054cfe98a372c15c899d74\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
43            android:exported="false" />
43-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd38f0899b054cfe98a372c15c899d74\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
44
45        <provider
45-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
46            android:name="androidx.startup.InitializationProvider"
46-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
47            android:authorities="com.example.demo1.androidx-startup"
47-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
48            android:exported="false" >
48-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
49            <meta-data
49-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
50                android:name="androidx.emoji2.text.EmojiCompatInitializer"
50-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
51                android:value="androidx.startup" />
51-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
52            <meta-data
52-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c7445ae1051a228a11db3cd72217b35\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
53                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
53-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c7445ae1051a228a11db3cd72217b35\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
54                android:value="androidx.startup" />
54-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c7445ae1051a228a11db3cd72217b35\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
55            <meta-data
55-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
56-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
57                android:value="androidx.startup" />
57-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
58        </provider>
59
60        <uses-library
60-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
61            android:name="androidx.window.extensions"
61-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
62            android:required="false" />
62-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
63        <uses-library
63-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
64            android:name="androidx.window.sidecar"
64-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
65            android:required="false" />
65-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
66
67        <receiver
67-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
68            android:name="androidx.profileinstaller.ProfileInstallReceiver"
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
69            android:directBootAware="false"
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
70            android:enabled="true"
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
71            android:exported="true"
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
72            android:permission="android.permission.DUMP" >
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
73            <intent-filter>
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
74                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
75            </intent-filter>
76            <intent-filter>
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
77                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
78            </intent-filter>
79            <intent-filter>
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
80                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
81            </intent-filter>
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
83                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
84            </intent-filter>
85        </receiver>
86    </application>
87
88</manifest>
