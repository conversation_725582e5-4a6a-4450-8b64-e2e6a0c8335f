1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.demo1"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="35" />
10
11    <permission
11-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.demo1.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.demo1.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:5:5-35:19
18        android:allowBackup="true"
18-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:12:9-35
28        android:theme="@style/Theme.Demo1" >
28-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:13:9-43
29
30        <!-- Login Activity - Launcher Activity -->
31        <activity
31-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:16:9-23:20
32            android:name="com.example.demo1.activities.LoginActivity"
32-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:17:13-53
33            android:exported="true" >
33-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:18:13-36
34            <intent-filter>
34-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:19:13-22:29
35                <action android:name="android.intent.action.MAIN" />
35-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:20:17-69
35-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:20:25-66
36
37                <category android:name="android.intent.category.LAUNCHER" />
37-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:21:17-77
37-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:21:27-74
38            </intent-filter>
39        </activity>
40
41        <!-- Main Activity -->
42        <activity
42-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:26:9-28:40
43            android:name="com.example.demo1.MainActivity"
43-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:27:13-41
44            android:exported="false" />
44-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:28:13-37
45
46        <!-- Add/Edit Task Activity -->
47        <activity
47-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:31:9-34:58
48            android:name="com.example.demo1.activities.AddEditTaskActivity"
48-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:32:13-59
49            android:exported="false"
49-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:33:13-37
50            android:parentActivityName="com.example.demo1.MainActivity" />
50-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:34:13-55
51
52        <service
52-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd38f0899b054cfe98a372c15c899d74\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
53            android:name="androidx.room.MultiInstanceInvalidationService"
53-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd38f0899b054cfe98a372c15c899d74\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
54            android:directBootAware="true"
54-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd38f0899b054cfe98a372c15c899d74\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
55            android:exported="false" />
55-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd38f0899b054cfe98a372c15c899d74\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
56
57        <provider
57-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
58            android:name="androidx.startup.InitializationProvider"
58-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
59            android:authorities="com.example.demo1.androidx-startup"
59-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
60            android:exported="false" >
60-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
61            <meta-data
61-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.emoji2.text.EmojiCompatInitializer"
62-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
63                android:value="androidx.startup" />
63-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
64            <meta-data
64-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c7445ae1051a228a11db3cd72217b35\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
65                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
65-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c7445ae1051a228a11db3cd72217b35\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
66                android:value="androidx.startup" />
66-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c7445ae1051a228a11db3cd72217b35\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
67            <meta-data
67-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
68                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
69                android:value="androidx.startup" />
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
70        </provider>
71
72        <uses-library
72-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
73            android:name="androidx.window.extensions"
73-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
74            android:required="false" />
74-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
75        <uses-library
75-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
76            android:name="androidx.window.sidecar"
76-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
77            android:required="false" />
77-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
78
79        <receiver
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
80            android:name="androidx.profileinstaller.ProfileInstallReceiver"
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
81            android:directBootAware="false"
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
82            android:enabled="true"
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
83            android:exported="true"
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
84            android:permission="android.permission.DUMP" >
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
86                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
87            </intent-filter>
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
89                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
90            </intent-filter>
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
92                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
93            </intent-filter>
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
95                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
96            </intent-filter>
97        </receiver>
98    </application>
99
100</manifest>
