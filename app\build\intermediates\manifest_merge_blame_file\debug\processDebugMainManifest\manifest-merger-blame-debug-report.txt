1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.demo1"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="35" />
10
11    <permission
11-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.demo1.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.demo1.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:5:5-35:19
18        android:allowBackup="true"
18-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:12:9-35
28        android:testOnly="true"
29        android:theme="@style/Theme.Demo1" >
29-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:13:9-43
30
31        <!-- Login Activity - Launcher Activity -->
32        <activity
32-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:16:9-23:20
33            android:name="com.example.demo1.activities.LoginActivity"
33-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:17:13-53
34            android:exported="true" >
34-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:18:13-36
35            <intent-filter>
35-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:19:13-22:29
36                <action android:name="android.intent.action.MAIN" />
36-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:20:17-69
36-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:20:25-66
37
38                <category android:name="android.intent.category.LAUNCHER" />
38-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:21:17-77
38-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:21:27-74
39            </intent-filter>
40        </activity>
41
42        <!-- Main Activity -->
43        <activity
43-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:26:9-28:40
44            android:name="com.example.demo1.MainActivity"
44-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:27:13-41
45            android:exported="false" />
45-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:28:13-37
46
47        <!-- Add/Edit Task Activity -->
48        <activity
48-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:31:9-34:58
49            android:name="com.example.demo1.activities.AddEditTaskActivity"
49-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:32:13-59
50            android:exported="false"
50-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:33:13-37
51            android:parentActivityName="com.example.demo1.MainActivity" />
51-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:34:13-55
52
53        <service
53-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd38f0899b054cfe98a372c15c899d74\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
54            android:name="androidx.room.MultiInstanceInvalidationService"
54-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd38f0899b054cfe98a372c15c899d74\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
55            android:directBootAware="true"
55-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd38f0899b054cfe98a372c15c899d74\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
56            android:exported="false" />
56-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd38f0899b054cfe98a372c15c899d74\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
57
58        <provider
58-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
59            android:name="androidx.startup.InitializationProvider"
59-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
60            android:authorities="com.example.demo1.androidx-startup"
60-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
61            android:exported="false" >
61-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
62            <meta-data
62-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
63                android:name="androidx.emoji2.text.EmojiCompatInitializer"
63-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
64                android:value="androidx.startup" />
64-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
65            <meta-data
65-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c7445ae1051a228a11db3cd72217b35\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
66                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
66-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c7445ae1051a228a11db3cd72217b35\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
67                android:value="androidx.startup" />
67-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c7445ae1051a228a11db3cd72217b35\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
68            <meta-data
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
69                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
70                android:value="androidx.startup" />
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
71        </provider>
72
73        <uses-library
73-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
74            android:name="androidx.window.extensions"
74-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
75            android:required="false" />
75-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
76        <uses-library
76-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
77            android:name="androidx.window.sidecar"
77-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
78            android:required="false" />
78-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
79
80        <receiver
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
81            android:name="androidx.profileinstaller.ProfileInstallReceiver"
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
82            android:directBootAware="false"
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
83            android:enabled="true"
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
84            android:exported="true"
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
85            android:permission="android.permission.DUMP" >
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
87                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
88            </intent-filter>
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
90                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
91            </intent-filter>
92            <intent-filter>
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
93                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
94            </intent-filter>
95            <intent-filter>
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
96                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
97            </intent-filter>
98        </receiver>
99    </application>
100
101</manifest>
