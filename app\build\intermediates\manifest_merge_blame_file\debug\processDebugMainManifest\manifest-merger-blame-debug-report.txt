1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.demo1"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="35" />
10
11    <permission
11-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.demo1.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.demo1.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:5:5-38:19
18        android:allowBackup="true"
18-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da51fb8d78929212dd6b1464ada4ac4\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:12:9-35
28        android:theme="@style/Theme.Demo1" >
28-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:13:9-43
29
30        <!-- Login Activity - Launcher Activity -->
31        <activity
31-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:16:9-24:20
32            android:name="com.example.demo1.activities.LoginActivity"
32-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:17:13-53
33            android:exported="true"
33-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:18:13-36
34            android:theme="@style/Theme.Demo1.NoActionBar" >
34-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:19:13-59
35            <intent-filter>
35-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:20:13-23:29
36                <action android:name="android.intent.action.MAIN" />
36-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:21:17-69
36-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:21:25-66
37
38                <category android:name="android.intent.category.LAUNCHER" />
38-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:22:17-77
38-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:22:27-74
39            </intent-filter>
40        </activity>
41
42        <!-- Main Activity -->
43        <activity
43-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:27:9-30:62
44            android:name="com.example.demo1.MainActivity"
44-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:28:13-41
45            android:exported="false"
45-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:29:13-37
46            android:theme="@style/Theme.Demo1.NoActionBar" />
46-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:30:13-59
47
48        <!-- Add/Edit Task Activity -->
49        <activity
49-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:33:9-37:58
50            android:name="com.example.demo1.activities.AddEditTaskActivity"
50-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:34:13-59
51            android:exported="false"
51-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:35:13-37
52            android:parentActivityName="com.example.demo1.MainActivity"
52-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:37:13-55
53            android:theme="@style/Theme.Demo1.NoActionBar" />
53-->D:\TienDuong\Users\TienDuong\AndroidStudioProjects\app\src\main\AndroidManifest.xml:36:13-59
54
55        <service
55-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd38f0899b054cfe98a372c15c899d74\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
56            android:name="androidx.room.MultiInstanceInvalidationService"
56-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd38f0899b054cfe98a372c15c899d74\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
57            android:directBootAware="true"
57-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd38f0899b054cfe98a372c15c899d74\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
58            android:exported="false" />
58-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd38f0899b054cfe98a372c15c899d74\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
59
60        <provider
60-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
61            android:name="androidx.startup.InitializationProvider"
61-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
62            android:authorities="com.example.demo1.androidx-startup"
62-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
63            android:exported="false" >
63-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
64            <meta-data
64-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
65                android:name="androidx.emoji2.text.EmojiCompatInitializer"
65-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
66                android:value="androidx.startup" />
66-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25712acec991d757c5dd184ad779cb81\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
67            <meta-data
67-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c7445ae1051a228a11db3cd72217b35\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
68                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
68-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c7445ae1051a228a11db3cd72217b35\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
69                android:value="androidx.startup" />
69-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c7445ae1051a228a11db3cd72217b35\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
70            <meta-data
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
71                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
72                android:value="androidx.startup" />
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
73        </provider>
74
75        <uses-library
75-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
76            android:name="androidx.window.extensions"
76-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
77            android:required="false" />
77-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
78        <uses-library
78-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
79            android:name="androidx.window.sidecar"
79-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
80            android:required="false" />
80-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb9bf061da9f4b14bb1442e7a413ba72\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
81
82        <receiver
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
83            android:name="androidx.profileinstaller.ProfileInstallReceiver"
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
84            android:directBootAware="false"
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
85            android:enabled="true"
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
86            android:exported="true"
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
87            android:permission="android.permission.DUMP" >
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
89                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
90            </intent-filter>
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
92                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
93            </intent-filter>
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
95                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
96            </intent-filter>
97            <intent-filter>
97-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
98                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35da08792659b3e76423dc3745b71a69\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
99            </intent-filter>
100        </receiver>
101    </application>
102
103</manifest>
