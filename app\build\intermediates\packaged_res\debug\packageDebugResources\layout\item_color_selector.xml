<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_color"
    android:layout_width="32dp"
    android:layout_height="32dp"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp"
    app:strokeWidth="0dp"
    app:strokeColor="@android:color/black">

    <View
        android:id="@+id/view_color"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</com.google.android.material.card.MaterialCardView>
