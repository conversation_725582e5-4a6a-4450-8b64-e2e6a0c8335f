R_DEF: Internal format may change without notice
local
color accent
color accent_light
color background
color black
color bottom_nav_color
color error
color gray_100
color gray_200
color gray_300
color gray_400
color gray_50
color gray_500
color gray_600
color gray_700
color gray_800
color gray_900
color info
color primary
color primary_dark
color primary_light
color priority_high
color priority_low
color priority_medium
color success
color surface
color text_hint
color text_primary
color text_secondary
color warning
color white
drawable bg_priority_high
drawable bg_priority_low
drawable bg_priority_medium
drawable ic_add
drawable ic_add_category
drawable ic_calendar
drawable ic_calendar_small
drawable ic_category
drawable ic_description
drawable ic_edit
drawable ic_email
drawable ic_home
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_lock
drawable ic_logout
drawable ic_person
drawable ic_profile
drawable ic_task
id bottom_navigation
id btn_cancel
id btn_edit_profile
id btn_login
id btn_logout
id btn_register
id btn_save
id btn_select_date
id btn_switch_mode
id calendar_view
id card_category
id card_color
id card_date_info
id card_icon
id card_login
id card_statistics
id card_task
id card_user_info
id chip_all
id chip_completed
id chip_group_filter
id chip_incomplete
id et_category_name
id et_description
id et_email
id et_full_name
id et_password
id et_title
id et_username
id fab_add_category
id fab_add_subtask
id fab_add_task
id fragment_container
id layout_buttons
id layout_category_priority
id layout_description
id layout_due_date
id layout_email
id layout_empty_state
id layout_full_name
id layout_password
id layout_subtasks
id layout_title
id main
id nav_calendar
id nav_home
id nav_profile
id progress_bar
id progress_bar_task
id rv_categories
id rv_colors
id rv_icons
id rv_tasks
id spinner_category
id spinner_priority
id tv_app_name
id tv_categories_label
id tv_category_icon
id tv_category_name
id tv_completed_tasks
id tv_completion_rate
id tv_due_date
id tv_email
id tv_full_name
id tv_header
id tv_icon
id tv_incomplete_tasks
id tv_join_date
id tv_logo
id tv_progress_label
id tv_selected_date
id tv_subtasks_label
id tv_switch_text
id tv_task_count
id tv_task_description
id tv_task_due_date
id tv_task_priority
id tv_task_progress
id tv_task_title
id tv_tasks_label
id tv_title
id tv_total_categories
id tv_total_tasks
id tv_username
id view_color
layout activity_add_edit_task
layout activity_login
layout activity_main
layout dialog_add_category
layout fragment_calendar
layout fragment_home
layout fragment_profile
layout item_category
layout item_color_selector
layout item_icon_selector
layout item_task
menu bottom_navigation_menu
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
style Base.Theme.Demo1
style Theme.Demo1
xml backup_rules
xml data_extraction_rules
