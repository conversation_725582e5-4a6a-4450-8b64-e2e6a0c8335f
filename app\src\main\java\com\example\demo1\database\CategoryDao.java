package com.example.demo1.database;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.demo1.models.Category;

import java.util.List;

/**
 * DAO (Data Access Object) cho Category Entity
 * Chứa các phương thức để thao tác với bảng categories trong database
 */
@Dao
public interface CategoryDao {
    
    /**
     * Thêm category mới vào database
     * @param category Category cần thêm
     * @return ID của category vừa được thêm
     */
    @Insert
    long insertCategory(Category category);
    
    /**
     * Cập nhật thông tin category
     * @param category Category cần cập nhật
     * @return Số dòng bị ảnh hưởng
     */
    @Update
    int updateCategory(Category category);
    
    /**
     * Xóa category khỏi database
     * @param category Category cần xóa
     * @return Số dòng bị ảnh hưởng
     */
    @Delete
    int deleteCategory(Category category);
    
    /**
     * Lấy tất cả category của một user
     * @param userId ID của user
     * @return LiveData chứa danh sách category
     */
    @Query("SELECT * FROM categories WHERE user_id = :userId ORDER BY is_default DESC, created_at ASC")
    LiveData<List<Category>> getCategoriesByUserId(int userId);
    
    /**
     * Lấy category theo ID
     * @param categoryId ID của category
     * @return LiveData chứa category
     */
    @Query("SELECT * FROM categories WHERE category_id = :categoryId")
    LiveData<Category> getCategoryById(int categoryId);
    
    /**
     * Lấy category mặc định "Tất cả" của user
     * @param userId ID của user
     * @return Category mặc định
     */
    @Query("SELECT * FROM categories WHERE user_id = :userId AND is_default = 1 LIMIT 1")
    Category getDefaultCategoryByUserId(int userId);
    
    /**
     * Lấy các category thông thường (không phải mặc định) của user
     * @param userId ID của user
     * @return LiveData chứa danh sách category thông thường
     */
    @Query("SELECT * FROM categories WHERE user_id = :userId AND is_default = 0 ORDER BY created_at ASC")
    LiveData<List<Category>> getNormalCategoriesByUserId(int userId);
    
    /**
     * Kiểm tra xem tên category đã tồn tại chưa (trong phạm vi user)
     * @param userId ID của user
     * @param name Tên category cần kiểm tra
     * @return Số lượng category có tên này
     */
    @Query("SELECT COUNT(*) FROM categories WHERE user_id = :userId AND name = :name")
    int checkCategoryNameExists(int userId, String name);
    
    /**
     * Kiểm tra xem tên category đã tồn tại chưa (trừ category hiện tại)
     * @param userId ID của user
     * @param name Tên category cần kiểm tra
     * @param excludeCategoryId ID category cần loại trừ
     * @return Số lượng category có tên này
     */
    @Query("SELECT COUNT(*) FROM categories WHERE user_id = :userId AND name = :name AND category_id != :excludeCategoryId")
    int checkCategoryNameExistsExclude(int userId, String name, int excludeCategoryId);
    
    /**
     * Đếm số lượng category của user
     * @param userId ID của user
     * @return Số lượng category
     */
    @Query("SELECT COUNT(*) FROM categories WHERE user_id = :userId")
    LiveData<Integer> getCategoryCountByUserId(int userId);
    
    /**
     * Xóa tất cả category của user (khi xóa user)
     * @param userId ID của user
     * @return Số dòng bị ảnh hưởng
     */
    @Query("DELETE FROM categories WHERE user_id = :userId")
    int deleteAllCategoriesByUserId(int userId);
    
    /**
     * Lấy category theo tên (trong phạm vi user)
     * @param userId ID của user
     * @param name Tên category
     * @return Category tìm được
     */
    @Query("SELECT * FROM categories WHERE user_id = :userId AND name = :name LIMIT 1")
    Category getCategoryByName(int userId, String name);
}
