<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".activities.AddEditTaskActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Header -->
        <TextView
            android:id="@+id/tv_header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Thêm công việc mới"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Task Title -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/layout_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:hint="Tiêu đề công việc *"
            app:startIconDrawable="@drawable/ic_task"
            app:layout_constraintTop_toBottomOf="@+id/tv_header"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLines="2" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Task Description -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/layout_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:hint="Mô tả (tùy chọn)"
            app:startIconDrawable="@drawable/ic_description"
            app:layout_constraintTop_toBottomOf="@+id/layout_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine"
                android:maxLines="3" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Category and Priority Row -->
        <LinearLayout
            android:id="@+id/layout_category_priority"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal"
            android:weightSum="2"
            app:layout_constraintTop_toBottomOf="@+id/layout_description"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <!-- Category Spinner -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:hint="Chủ đề *"
                style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu">

                <AutoCompleteTextView
                    android:id="@+id/spinner_category"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Priority Spinner -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:hint="Mức độ ưu tiên"
                style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu">

                <AutoCompleteTextView
                    android:id="@+id/spinner_priority"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- Due Date -->
        <LinearLayout
            android:id="@+id/layout_due_date"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            app:layout_constraintTop_toBottomOf="@+id/layout_category_priority"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="📅"
                android:textSize="24sp"
                android:layout_marginEnd="12dp" />

            <TextView
                android:id="@+id/tv_due_date"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Chọn ngày hết hạn"
                android:textSize="16sp"
                android:textColor="@color/text_secondary" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_select_date"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Chọn ngày"
                app:cornerRadius="8dp" />

        </LinearLayout>

        <!-- SubTasks Section -->
        <TextView
            android:id="@+id/tv_subtasks_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="Công việc con"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            app:layout_constraintTop_toBottomOf="@+id/layout_due_date"
            app:layout_constraintStart_toStartOf="parent" />

        <LinearLayout
            android:id="@+id/layout_subtasks"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="vertical"
            app:layout_constraintTop_toBottomOf="@+id/tv_subtasks_label"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Add SubTask Button -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab_add_subtask"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:src="@drawable/ic_add"
            android:contentDescription="Thêm công việc con"
            app:backgroundTint="@color/accent"
            app:tint="@color/white"
            app:fabSize="mini"
            app:layout_constraintTop_toBottomOf="@+id/layout_subtasks"
            app:layout_constraintStart_toStartOf="parent" />

        <!-- Action Buttons -->
        <LinearLayout
            android:id="@+id/layout_buttons"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:orientation="horizontal"
            android:weightSum="2"
            app:layout_constraintTop_toBottomOf="@+id/fab_add_subtask"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <!-- Cancel Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_cancel"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="Hủy"
                android:textSize="16sp"
                app:cornerRadius="8dp" />

            <!-- Save Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_save"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="Lưu"
                android:textSize="16sp"
                app:backgroundTint="@color/primary"
                app:cornerRadius="8dp" />

        </LinearLayout>

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@+id/layout_buttons"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
