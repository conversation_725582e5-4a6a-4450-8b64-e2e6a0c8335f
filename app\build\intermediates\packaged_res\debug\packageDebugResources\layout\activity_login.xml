<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".activities.LoginActivity">

    <!-- Logo/Icon -->
    <TextView
        android:id="@+id/tv_logo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="📝"
        android:textSize="64sp"
        android:layout_marginTop="64dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- App Name -->
    <TextView
        android:id="@+id/tv_app_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="TodoList"
        android:textSize="28sp"
        android:textStyle="bold"
        android:textColor="@color/primary"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_logo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Login/Register Card -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_login"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="48dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        app:cardBackgroundColor="@color/white"
        app:layout_constraintTop_toBottomOf="@+id/tv_app_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Title -->
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Đăng Nhập"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="24dp" />

            <!-- Username Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Tên đăng nhập"
                app:startIconDrawable="@drawable/ic_person">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_username"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Email Input (for register mode) -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/layout_email"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Email"
                android:visibility="gone"
                app:startIconDrawable="@drawable/ic_email">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textEmailAddress"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Full Name Input (for register mode) -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/layout_full_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:hint="Họ và tên"
                android:visibility="gone"
                app:startIconDrawable="@drawable/ic_person">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_full_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPersonName"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Login Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_login"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Đăng Nhập"
                android:textSize="16sp"
                android:textStyle="bold"
                app:backgroundTint="@color/primary"
                app:cornerRadius="8dp" />

            <!-- Register Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_register"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Đăng Ký"
                android:textSize="16sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:backgroundTint="@color/primary"
                app:cornerRadius="8dp" />

            <!-- Progress Bar -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="16dp"
                android:visibility="gone" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Switch Mode Section -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="24dp"
        app:layout_constraintTop_toBottomOf="@+id/card_login"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:id="@+id/tv_switch_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Chưa có tài khoản?"
            android:textSize="14sp"
            android:textColor="@color/text_secondary" />

        <TextView
            android:id="@+id/btn_switch_mode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="Đăng ký ngay"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/primary"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:padding="4dp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
