[{"merged": "com.example.demo1.app-debug-48:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.demo1.app-main-50:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.demo1.app-debug-48:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.demo1.app-main-50:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.demo1.app-debug-48:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.demo1.app-main-50:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.demo1.app-debug-48:/xml_data_extraction_rules.xml.flat", "source": "com.example.demo1.app-main-50:/xml/data_extraction_rules.xml"}, {"merged": "com.example.demo1.app-debug-48:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.demo1.app-main-50:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.demo1.app-debug-48:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.demo1.app-main-50:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.demo1.app-debug-48:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.demo1.app-main-50:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.demo1.app-debug-48:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.demo1.app-main-50:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.demo1.app-debug-48:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.demo1.app-main-50:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.demo1.app-debug-48:/xml_backup_rules.xml.flat", "source": "com.example.demo1.app-main-50:/xml/backup_rules.xml"}, {"merged": "com.example.demo1.app-debug-48:/mipmap-anydpi_ic_launcher_round.xml.flat", "source": "com.example.demo1.app-main-50:/mipmap-anydpi/ic_launcher_round.xml"}, {"merged": "com.example.demo1.app-debug-48:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.demo1.app-main-50:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.demo1.app-debug-48:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.demo1.app-main-50:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.demo1.app-debug-48:/layout_activity_main.xml.flat", "source": "com.example.demo1.app-main-50:/layout/activity_main.xml"}, {"merged": "com.example.demo1.app-debug-48:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.demo1.app-main-50:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.demo1.app-debug-48:/drawable_ic_launcher_background.xml.flat", "source": "com.example.demo1.app-main-50:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.demo1.app-debug-48:/mipmap-anydpi_ic_launcher.xml.flat", "source": "com.example.demo1.app-main-50:/mipmap-anydpi/ic_launcher.xml"}]