[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-debug-48:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-main-50:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-debug-48:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-main-50:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-debug-48:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-main-50:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "com.example.demo1.app-debug-48:/drawable_ic_add.xml.flat", "source": "com.example.demo1.app-main-50:/drawable/ic_add.xml"}, {"merged": "com.example.demo1.app-debug-48:/layout_item_task.xml.flat", "source": "com.example.demo1.app-main-50:/layout/item_task.xml"}, {"merged": "com.example.demo1.app-debug-48:/drawable_ic_home.xml.flat", "source": "com.example.demo1.app-main-50:/drawable/ic_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-debug-48:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-main-50:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-debug-48:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-main-50:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-debug-48:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-main-50:\\xml\\backup_rules.xml"}, {"merged": "com.example.demo1.app-debug-48:/layout_activity_login.xml.flat", "source": "com.example.demo1.app-main-50:/layout/activity_login.xml"}, {"merged": "com.example.demo1.app-debug-48:/drawable_ic_person.xml.flat", "source": "com.example.demo1.app-main-50:/drawable/ic_person.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-debug-48:\\mipmap-anydpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-main-50:\\mipmap-anydpi\\ic_launcher_round.xml"}, {"merged": "com.example.demo1.app-debug-48:/layout_fragment_calendar.xml.flat", "source": "com.example.demo1.app-main-50:/layout/fragment_calendar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-debug-48:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-main-50:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "com.example.demo1.app-debug-48:/drawable_ic_logout.xml.flat", "source": "com.example.demo1.app-main-50:/drawable/ic_logout.xml"}, {"merged": "com.example.demo1.app-debug-48:/drawable_bg_priority_medium.xml.flat", "source": "com.example.demo1.app-main-50:/drawable/bg_priority_medium.xml"}, {"merged": "com.example.demo1.app-debug-48:/layout_activity_add_edit_task.xml.flat", "source": "com.example.demo1.app-main-50:/layout/activity_add_edit_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-debug-48:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-main-50:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-debug-48:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-main-50:\\xml\\data_extraction_rules.xml"}, {"merged": "com.example.demo1.app-debug-48:/drawable_ic_email.xml.flat", "source": "com.example.demo1.app-main-50:/drawable/ic_email.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-debug-48:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-main-50:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "com.example.demo1.app-debug-48:/drawable_ic_task.xml.flat", "source": "com.example.demo1.app-main-50:/drawable/ic_task.xml"}, {"merged": "com.example.demo1.app-debug-48:/layout_fragment_home.xml.flat", "source": "com.example.demo1.app-main-50:/layout/fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-debug-48:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-main-50:\\layout\\activity_main.xml"}, {"merged": "com.example.demo1.app-debug-48:/drawable_bg_priority_high.xml.flat", "source": "com.example.demo1.app-main-50:/drawable/bg_priority_high.xml"}, {"merged": "com.example.demo1.app-debug-48:/drawable_ic_calendar_small.xml.flat", "source": "com.example.demo1.app-main-50:/drawable/ic_calendar_small.xml"}, {"merged": "com.example.demo1.app-debug-48:/drawable_ic_profile.xml.flat", "source": "com.example.demo1.app-main-50:/drawable/ic_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-debug-48:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-main-50:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "com.example.demo1.app-debug-48:/drawable_ic_add_category.xml.flat", "source": "com.example.demo1.app-main-50:/drawable/ic_add_category.xml"}, {"merged": "com.example.demo1.app-debug-48:/color_bottom_nav_color.xml.flat", "source": "com.example.demo1.app-main-50:/color/bottom_nav_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-debug-48:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-main-50:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-debug-48:\\mipmap-anydpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-main-50:\\mipmap-anydpi\\ic_launcher.xml"}, {"merged": "com.example.demo1.app-debug-48:/drawable_ic_description.xml.flat", "source": "com.example.demo1.app-main-50:/drawable/ic_description.xml"}, {"merged": "com.example.demo1.app-debug-48:/layout_item_category.xml.flat", "source": "com.example.demo1.app-main-50:/layout/item_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-debug-48:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-main-50:\\drawable\\ic_launcher_background.xml"}, {"merged": "com.example.demo1.app-debug-48:/menu_bottom_navigation_menu.xml.flat", "source": "com.example.demo1.app-main-50:/menu/bottom_navigation_menu.xml"}, {"merged": "com.example.demo1.app-debug-48:/drawable_bg_priority_low.xml.flat", "source": "com.example.demo1.app-main-50:/drawable/bg_priority_low.xml"}, {"merged": "com.example.demo1.app-debug-48:/drawable_ic_edit.xml.flat", "source": "com.example.demo1.app-main-50:/drawable/ic_edit.xml"}, {"merged": "com.example.demo1.app-debug-48:/drawable_ic_calendar.xml.flat", "source": "com.example.demo1.app-main-50:/drawable/ic_calendar.xml"}, {"merged": "com.example.demo1.app-debug-48:/layout_activity_main.xml.flat", "source": "com.example.demo1.app-main-50:/layout/activity_main.xml"}, {"merged": "com.example.demo1.app-debug-48:/layout_fragment_profile.xml.flat", "source": "com.example.demo1.app-main-50:/layout/fragment_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-debug-48:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.demo1.app-main-50:\\mipmap-mdpi\\ic_launcher_round.webp"}]