<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".fragments.ProfileFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Header -->
        <TextView
            android:id="@+id/tv_header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Thông tin cá nhân"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- User Info Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_user_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/white"
            app:layout_constraintTop_toBottomOf="@+id/tv_header"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- Avatar -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="👤"
                    android:textSize="48sp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginBottom="16dp" />

                <!-- Username -->
                <TextView
                    android:id="@+id/tv_username"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginBottom="4dp"
                    tools:text="john_doe" />

                <!-- Full Name -->
                <TextView
                    android:id="@+id/tv_full_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="16sp"
                    android:textColor="@color/text_secondary"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginBottom="4dp"
                    tools:text="John Doe" />

                <!-- Email -->
                <TextView
                    android:id="@+id/tv_email"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:textColor="@color/text_hint"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginBottom="8dp"
                    tools:text="<EMAIL>" />

                <!-- Join Date -->
                <TextView
                    android:id="@+id/tv_join_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:textColor="@color/text_hint"
                    android:layout_gravity="center_horizontal"
                    tools:text="Tham gia: 01/01/2024" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Statistics Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_statistics"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/white"
            app:layout_constraintTop_toBottomOf="@+id/card_user_info"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Thống kê"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="16dp" />

                <!-- Statistics Grid -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <!-- Left Column -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <!-- Total Tasks -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="12dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Tổng công việc:"
                                android:textSize="14sp"
                                android:textColor="@color/text_secondary" />

                            <TextView
                                android:id="@+id/tv_total_tasks"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="@color/text_primary"
                                tools:text="15" />

                        </LinearLayout>

                        <!-- Completed Tasks -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="12dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Đã hoàn thành:"
                                android:textSize="14sp"
                                android:textColor="@color/text_secondary" />

                            <TextView
                                android:id="@+id/tv_completed_tasks"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="@color/success"
                                tools:text="10" />

                        </LinearLayout>

                        <!-- Incomplete Tasks -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Chưa hoàn thành:"
                                android:textSize="14sp"
                                android:textColor="@color/text_secondary" />

                            <TextView
                                android:id="@+id/tv_incomplete_tasks"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="@color/warning"
                                tools:text="5" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Right Column -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_marginStart="16dp">

                        <!-- Total Categories -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="12dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Chủ đề:"
                                android:textSize="14sp"
                                android:textColor="@color/text_secondary" />

                            <TextView
                                android:id="@+id/tv_total_categories"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="@color/text_primary"
                                tools:text="5" />

                        </LinearLayout>

                        <!-- Completion Rate -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Tỷ lệ hoàn thành:"
                                android:textSize="14sp"
                                android:textColor="@color/text_secondary" />

                            <TextView
                                android:id="@+id/tv_completion_rate"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="@color/primary"
                                tools:text="67%" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:orientation="vertical"
            app:layout_constraintTop_toBottomOf="@+id/card_statistics"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <!-- Edit Profile Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_edit_profile"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Chỉnh sửa thông tin"
                android:textSize="16sp"
                android:layout_marginBottom="12dp"
                app:backgroundTint="@color/primary"
                app:cornerRadius="8dp"
                app:icon="@drawable/ic_edit" />

            <!-- Logout Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_logout"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Đăng xuất"
                android:textSize="16sp"
                android:textColor="@color/error"
                app:strokeColor="@color/error"
                app:cornerRadius="8dp"
                app:icon="@drawable/ic_logout"
                app:iconTint="@color/error" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
