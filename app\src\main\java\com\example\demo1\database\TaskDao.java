package com.example.demo1.database;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.demo1.models.Task;

import java.util.List;

/**
 * DAO (Data Access Object) cho Task Entity
 * Chứa các phương thức để thao tác với bảng tasks trong database
 */
@Dao
public interface TaskDao {
    
    /**
     * Thêm task mới vào database
     * @param task Task cần thêm
     * @return ID của task vừa được thêm
     */
    @Insert
    long insertTask(Task task);
    
    /**
     * Cập nhật thông tin task
     * @param task Task cần cập nhật
     * @return Số dòng bị ảnh hưởng
     */
    @Update
    int updateTask(Task task);
    
    /**
     * Xóa task khỏi database
     * @param task Task cần xóa
     * @return Số dòng bị ảnh hưởng
     */
    @Delete
    int deleteTask(Task task);
    
    /**
     * <PERSON><PERSON>y tất cả task của một user
     * @param userId ID của user
     * @return LiveData chứa danh sách task
     */
    @Query("SELECT * FROM tasks WHERE user_id = :userId ORDER BY created_at DESC")
    LiveData<List<Task>> getTasksByUserId(int userId);
    
    /**
     * Lấy task theo category
     * @param userId ID của user
     * @param categoryId ID của category
     * @return LiveData chứa danh sách task
     */
    @Query("SELECT * FROM tasks WHERE user_id = :userId AND category_id = :categoryId ORDER BY created_at DESC")
    LiveData<List<Task>> getTasksByCategoryId(int userId, int categoryId);
    
    /**
     * Lấy task theo ID
     * @param taskId ID của task
     * @return LiveData chứa task
     */
    @Query("SELECT * FROM tasks WHERE task_id = :taskId")
    LiveData<Task> getTaskById(int taskId);
    
    /**
     * Lấy task đã hoàn thành của user
     * @param userId ID của user
     * @return LiveData chứa danh sách task đã hoàn thành
     */
    @Query("SELECT * FROM tasks WHERE user_id = :userId AND is_completed = 1 ORDER BY updated_at DESC")
    LiveData<List<Task>> getCompletedTasksByUserId(int userId);
    
    /**
     * Lấy task chưa hoàn thành của user
     * @param userId ID của user
     * @return LiveData chứa danh sách task chưa hoàn thành
     */
    @Query("SELECT * FROM tasks WHERE user_id = :userId AND is_completed = 0 ORDER BY due_date ASC, created_at DESC")
    LiveData<List<Task>> getIncompleteTasksByUserId(int userId);
    
    /**
     * Lấy task theo ngày hết hạn
     * @param userId ID của user
     * @param startDate Ngày bắt đầu (timestamp)
     * @param endDate Ngày kết thúc (timestamp)
     * @return LiveData chứa danh sách task trong khoảng thời gian
     */
    @Query("SELECT * FROM tasks WHERE user_id = :userId AND due_date BETWEEN :startDate AND :endDate ORDER BY due_date ASC")
    LiveData<List<Task>> getTasksByDateRange(int userId, long startDate, long endDate);
    
    /**
     * Lấy task theo mức độ ưu tiên
     * @param userId ID của user
     * @param priority Mức độ ưu tiên
     * @return LiveData chứa danh sách task theo mức độ ưu tiên
     */
    @Query("SELECT * FROM tasks WHERE user_id = :userId AND priority = :priority ORDER BY due_date ASC")
    LiveData<List<Task>> getTasksByPriority(int userId, String priority);
    
    /**
     * Đánh dấu task hoàn thành
     * @param taskId ID của task
     * @param isCompleted Trạng thái hoàn thành
     * @return Số dòng bị ảnh hưởng
     */
    @Query("UPDATE tasks SET is_completed = :isCompleted, updated_at = :updatedAt WHERE task_id = :taskId")
    int updateTaskCompletionStatus(int taskId, boolean isCompleted, long updatedAt);
    
    /**
     * Đếm số lượng task của user
     * @param userId ID của user
     * @return Số lượng task
     */
    @Query("SELECT COUNT(*) FROM tasks WHERE user_id = :userId")
    LiveData<Integer> getTaskCountByUserId(int userId);
    
    /**
     * Đếm số lượng task đã hoàn thành của user
     * @param userId ID của user
     * @return Số lượng task đã hoàn thành
     */
    @Query("SELECT COUNT(*) FROM tasks WHERE user_id = :userId AND is_completed = 1")
    LiveData<Integer> getCompletedTaskCountByUserId(int userId);
    
    /**
     * Đếm số lượng task chưa hoàn thành của user
     * @param userId ID của user
     * @return Số lượng task chưa hoàn thành
     */
    @Query("SELECT COUNT(*) FROM tasks WHERE user_id = :userId AND is_completed = 0")
    LiveData<Integer> getIncompleteTaskCountByUserId(int userId);
    
    /**
     * Xóa tất cả task của category (khi xóa category)
     * @param categoryId ID của category
     * @return Số dòng bị ảnh hưởng
     */
    @Query("DELETE FROM tasks WHERE category_id = :categoryId")
    int deleteTasksByCategoryId(int categoryId);
    
    /**
     * Xóa tất cả task của user (khi xóa user)
     * @param userId ID của user
     * @return Số dòng bị ảnh hưởng
     */
    @Query("DELETE FROM tasks WHERE user_id = :userId")
    int deleteAllTasksByUserId(int userId);
}
