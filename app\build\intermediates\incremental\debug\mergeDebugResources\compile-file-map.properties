#Tue Jul 01 21:52:38 ICT 2025
com.example.demo1.app-main-50\:/menu/bottom_navigation_menu.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_bottom_navigation_menu.xml.flat
com.example.demo1.app-main-50\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.demo1.app-main-50\:/drawable/ic_task.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_task.xml.flat
com.example.demo1.app-main-50\:/drawable/ic_launcher_foreground.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.example.demo1.app-main-50\:/xml/data_extraction_rules.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.example.demo1.app-main-50\:/drawable/ic_email.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_email.xml.flat
com.example.demo1.app-main-50\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.demo1.app-main-50\:/drawable/bg_priority_high.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_priority_high.xml.flat
com.example.demo1.app-main-50\:/drawable/ic_add.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add.xml.flat
com.example.demo1.app-main-50\:/xml/backup_rules.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.demo1.app-main-50\:/mipmap-anydpi/ic_launcher.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher.xml.flat
com.example.demo1.app-main-50\:/drawable/bg_priority_low.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_priority_low.xml.flat
com.example.demo1.app-main-50\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.demo1.app-main-50\:/drawable/bg_priority_medium.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_priority_medium.xml.flat
com.example.demo1.app-main-50\:/drawable/ic_description.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_description.xml.flat
com.example.demo1.app-main-50\:/layout/fragment_calendar.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_calendar.xml.flat
com.example.demo1.app-main-50\:/drawable/ic_calendar_small.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_calendar_small.xml.flat
com.example.demo1.app-main-50\:/drawable/ic_launcher_background.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.demo1.app-main-50\:/layout/activity_login.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_login.xml.flat
com.example.demo1.app-main-50\:/drawable/ic_add_category.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add_category.xml.flat
com.example.demo1.app-main-50\:/mipmap-anydpi/ic_launcher_round.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher_round.xml.flat
com.example.demo1.app-main-50\:/drawable/ic_home.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_home.xml.flat
com.example.demo1.app-main-50\:/layout/item_task.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_task.xml.flat
com.example.demo1.app-main-50\:/color/bottom_nav_color.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_bottom_nav_color.xml.flat
com.example.demo1.app-main-50\:/layout/item_icon_selector.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_icon_selector.xml.flat
com.example.demo1.app-main-50\:/layout/activity_main.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.example.demo1.app-main-50\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.demo1.app-main-50\:/mipmap-xhdpi/ic_launcher.webp=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.demo1.app-main-50\:/mipmap-mdpi/ic_launcher.webp=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.demo1.app-main-50\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.demo1.app-main-50\:/layout/item_color_selector.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_color_selector.xml.flat
com.example.demo1.app-main-50\:/layout/fragment_profile.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_profile.xml.flat
com.example.demo1.app-main-50\:/drawable/ic_category.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category.xml.flat
com.example.demo1.app-main-50\:/drawable/ic_logout.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_logout.xml.flat
com.example.demo1.app-main-50\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.demo1.app-main-50\:/mipmap-hdpi/ic_launcher.webp=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.demo1.app-main-50\:/drawable/ic_person.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_person.xml.flat
com.example.demo1.app-main-50\:/layout/item_category.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_category.xml.flat
com.example.demo1.app-main-50\:/drawable/ic_profile.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_profile.xml.flat
com.example.demo1.app-main-50\:/drawable/ic_edit.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_edit.xml.flat
com.example.demo1.app-main-50\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.demo1.app-main-50\:/layout/fragment_home.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_home.xml.flat
com.example.demo1.app-main-50\:/drawable/ic_calendar.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_calendar.xml.flat
com.example.demo1.app-main-50\:/layout/dialog_add_category.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_category.xml.flat
com.example.demo1.app-main-50\:/layout/activity_add_edit_task.xml=D\:\\TienDuong\\Users\\TienDuong\\AndroidStudioProjects\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_add_edit_task.xml.flat
