package com.example.demo1.adapters;

import android.graphics.Color;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.example.demo1.R;

import java.util.List;

/**
 * IconSelectorAdapter - Adapter cho việc chọn icon trong dialog
 * Hiển thị danh sách icon dưới dạng grid với khả năng chọn
 */
public class IconSelectorAdapter extends RecyclerView.Adapter<IconSelectorAdapter.IconViewHolder> {
    
    private static final String TAG = "IconSelectorAdapter";
    
    // Interface cho icon selection
    public interface OnIconSelectedListener {
        void onIconSelected(String iconName);
    }
    
    // Data và listener
    private List<String> icons;
    private OnIconSelectedListener listener;
    private int selectedPosition = -1;
    private String selectedIcon = null;
    
    /**
     * Constructor
     * @param icons Danh sách tên icon
     * @param listener Listener cho icon selection
     */
    public IconSelectorAdapter(List<String> icons, OnIconSelectedListener listener) {
        this.icons = icons;
        this.listener = listener;
        Log.d(TAG, "IconSelectorAdapter: Khởi tạo với " + 
            (icons != null ? icons.size() : 0) + " icons");
    }
    
    @NonNull
    @Override
    public IconViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        Log.d(TAG, "onCreateViewHolder: Tạo ViewHolder mới");
        
        View view = LayoutInflater.from(parent.getContext())
            .inflate(R.layout.item_icon_selector, parent, false);
        return new IconViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull IconViewHolder holder, int position) {
        if (icons == null || position < 0 || position >= icons.size()) {
            Log.e(TAG, "onBindViewHolder: Position không hợp lệ - " + position);
            return;
        }
        
        String iconName = icons.get(position);
        Log.d(TAG, "onBindViewHolder: Bind icon - " + iconName + " tại position " + position);
        
        holder.bind(iconName, position == selectedPosition);
    }
    
    @Override
    public int getItemCount() {
        int count = icons != null ? icons.size() : 0;
        Log.d(TAG, "getItemCount: " + count);
        return count;
    }
    
    /**
     * Lấy icon được chọn
     * @return Tên icon được chọn hoặc null
     */
    public String getSelectedIcon() {
        Log.d(TAG, "getSelectedIcon: " + selectedIcon);
        return selectedIcon;
    }
    
    /**
     * ViewHolder cho Icon item
     */
    class IconViewHolder extends RecyclerView.ViewHolder {
        
        private CardView cardView;
        private TextView tvIcon;
        
        public IconViewHolder(@NonNull View itemView) {
            super(itemView);
            Log.d(TAG, "IconViewHolder: Khởi tạo ViewHolder");
            
            // Tìm các view
            cardView = itemView.findViewById(R.id.card_icon);
            tvIcon = itemView.findViewById(R.id.tv_icon);
            
            // Kiểm tra các view bắt buộc
            if (cardView == null || tvIcon == null) {
                Log.e(TAG, "IconViewHolder: Một số view bắt buộc không tìm thấy");
            }
            
            // Thiết lập click listener
            itemView.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && icons != null && listener != null) {
                    String iconName = icons.get(position);
                    Log.d(TAG, "IconViewHolder: Click vào icon - " + iconName);
                    
                    // Cập nhật selected position
                    int oldPosition = selectedPosition;
                    selectedPosition = position;
                    selectedIcon = iconName;
                    
                    // Cập nhật UI
                    notifyItemChanged(oldPosition);
                    notifyItemChanged(selectedPosition);
                    
                    // Gọi listener
                    listener.onIconSelected(iconName);
                }
            });
        }
        
        /**
         * Bind dữ liệu icon vào view
         * @param iconName Tên icon
         * @param isSelected Icon có được chọn không
         */
        public void bind(String iconName, boolean isSelected) {
            Log.d(TAG, "IconViewHolder.bind: Bind icon - " + iconName + ", selected: " + isSelected);
            
            try {
                // Hiển thị icon
                if (tvIcon != null) {
                    String iconText = getIconText(iconName);
                    tvIcon.setText(iconText);
                }
                
                // Thiết lập trạng thái selected
                if (cardView != null) {
                    if (isSelected) {
                        cardView.setCardBackgroundColor(Color.parseColor("#2196F3")); // Blue
                        cardView.setCardElevation(8f);
                    } else {
                        cardView.setCardBackgroundColor(Color.WHITE);
                        cardView.setCardElevation(2f);
                    }
                }
                
                Log.d(TAG, "IconViewHolder.bind: Bind thành công");
                
            } catch (Exception e) {
                Log.e(TAG, "IconViewHolder.bind: Lỗi khi bind dữ liệu", e);
            }
        }
        
        /**
         * Chuyển đổi tên icon thành text hiển thị
         * @param iconName Tên icon
         * @return Text hiển thị cho icon
         */
        private String getIconText(String iconName) {
            if (iconName == null) return "📁";
            
            // Mapping icon name thành emoji
            switch (iconName.toLowerCase()) {
                case "work":
                case "assignment":
                    return "💼";
                case "home":
                    return "🏠";
                case "school":
                case "book":
                    return "📚";
                case "fitness_center":
                case "sports":
                    return "💪";
                case "shopping_cart":
                case "store":
                    return "🛒";
                case "restaurant":
                case "local_cafe":
                    return "🍽️";
                case "flight":
                case "hotel":
                    return "✈️";
                case "favorite":
                case "star":
                    return "⭐";
                case "category":
                    return "📂";
                case "label":
                    return "🏷️";
                case "task_alt":
                case "checklist":
                    return "✅";
                case "event_note":
                    return "📝";
                case "family_restroom":
                    return "👨‍👩‍👧‍👦";
                case "child_care":
                    return "👶";
                case "elderly":
                    return "👴";
                case "pets":
                    return "🐕";
                case "library_books":
                    return "📖";
                case "quiz":
                    return "❓";
                case "science":
                    return "🔬";
                case "local_hospital":
                    return "🏥";
                case "healing":
                    return "💊";
                case "spa":
                    return "🧘";
                case "payment":
                    return "💳";
                case "account_balance":
                    return "🏦";
                case "savings":
                    return "💰";
                case "camera_alt":
                    return "📷";
                case "movie":
                    return "🎬";
                case "music_note":
                    return "🎵";
                case "cake":
                    return "🎂";
                case "local_pizza":
                    return "🍕";
                case "fastfood":
                    return "🍔";
                case "directions_car":
                    return "🚗";
                case "directions_bus":
                    return "🚌";
                case "motorcycle":
                    return "🏍️";
                case "pedal_bike":
                    return "🚴";
                case "train":
                    return "🚆";
                case "computer":
                    return "💻";
                case "phone_android":
                    return "📱";
                case "wifi":
                    return "📶";
                case "cloud":
                    return "☁️";
                case "code":
                    return "💻";
                case "wb_sunny":
                    return "☀️";
                case "nature":
                    return "🌿";
                case "park":
                    return "🌳";
                case "eco":
                    return "♻️";
                case "thumb_up":
                    return "👍";
                case "celebration":
                    return "🎉";
                case "mood":
                    return "😊";
                case "build":
                    return "🔧";
                case "settings":
                    return "⚙️";
                case "lightbulb":
                    return "💡";
                case "key":
                    return "🔑";
                case "lock":
                    return "🔒";
                case "bookmark":
                    return "🔖";
                case "flag":
                    return "🚩";
                case "priority_high":
                    return "❗";
                default:
                    return "📁";
            }
        }
    }
}
