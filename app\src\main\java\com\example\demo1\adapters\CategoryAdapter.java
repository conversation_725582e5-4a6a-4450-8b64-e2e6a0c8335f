package com.example.demo1.adapters;

import android.graphics.Color;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.example.demo1.R;
import com.example.demo1.models.Category;

import java.util.List;

/**
 * CategoryAdapter - Adapter cho RecyclerView hiển thị danh sách Category
 * Hiển thị category dưới dạng horizontal list với màu sắc và icon
 */
public class CategoryAdapter extends RecyclerView.Adapter<CategoryAdapter.CategoryViewHolder> {
    
    private static final String TAG = "CategoryAdapter";
    
    // Interface cho click listener
    public interface OnCategoryClickListener {
        void onCategoryClick(Category category);
    }
    
    // Data và listener
    private List<Category> categories;
    private OnCategoryClickListener clickListener;
    private int selectedPosition = 0; // Vị trí category được chọn
    
    /**
     * Constructor
     * @param categories Danh sách category
     * @param clickListener Listener cho click event
     */
    public CategoryAdapter(List<Category> categories, OnCategoryClickListener clickListener) {
        this.categories = categories;
        this.clickListener = clickListener;
        Log.d(TAG, "CategoryAdapter: Khởi tạo với " + 
            (categories != null ? categories.size() : 0) + " categories");
    }
    
    @NonNull
    @Override
    public CategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        Log.d(TAG, "onCreateViewHolder: Tạo ViewHolder mới");
        
        View view = LayoutInflater.from(parent.getContext())
            .inflate(R.layout.item_category, parent, false);
        return new CategoryViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull CategoryViewHolder holder, int position) {
        if (categories == null || position < 0 || position >= categories.size()) {
            Log.e(TAG, "onBindViewHolder: Position không hợp lệ - " + position);
            return;
        }
        
        Category category = categories.get(position);
        Log.d(TAG, "onBindViewHolder: Bind category - " + category.getName() + " tại position " + position);
        
        holder.bind(category, position == selectedPosition);
    }
    
    @Override
    public int getItemCount() {
        int count = categories != null ? categories.size() : 0;
        Log.d(TAG, "getItemCount: " + count);
        return count;
    }
    
    /**
     * Cập nhật danh sách category
     * @param newCategories Danh sách category mới
     */
    public void updateCategories(List<Category> newCategories) {
        Log.d(TAG, "updateCategories: Cập nhật với " + 
            (newCategories != null ? newCategories.size() : 0) + " categories");
        
        this.categories = newCategories;
        notifyDataSetChanged();
    }
    
    /**
     * Đặt category được chọn
     * @param position Vị trí category được chọn
     */
    public void setSelectedPosition(int position) {
        Log.d(TAG, "setSelectedPosition: Chọn position " + position);
        
        if (position >= 0 && position < getItemCount()) {
            int oldPosition = selectedPosition;
            selectedPosition = position;
            
            // Cập nhật UI cho position cũ và mới
            notifyItemChanged(oldPosition);
            notifyItemChanged(selectedPosition);
        }
    }
    
    /**
     * Lấy category được chọn
     * @return Category được chọn hoặc null
     */
    public Category getSelectedCategory() {
        if (categories != null && selectedPosition >= 0 && selectedPosition < categories.size()) {
            return categories.get(selectedPosition);
        }
        return null;
    }
    
    /**
     * ViewHolder cho Category item
     */
    class CategoryViewHolder extends RecyclerView.ViewHolder {
        
        private CardView cardView;
        private TextView tvCategoryName;
        private TextView tvCategoryIcon;
        
        public CategoryViewHolder(@NonNull View itemView) {
            super(itemView);
            Log.d(TAG, "CategoryViewHolder: Khởi tạo ViewHolder");
            
            // Tìm các view
            cardView = itemView.findViewById(R.id.card_category);
            tvCategoryName = itemView.findViewById(R.id.tv_category_name);
            tvCategoryIcon = itemView.findViewById(R.id.tv_category_icon);
            
            // Kiểm tra các view bắt buộc
            if (cardView == null || tvCategoryName == null) {
                Log.e(TAG, "CategoryViewHolder: Một số view bắt buộc không tìm thấy");
            }
            
            // Thiết lập click listener
            itemView.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && clickListener != null && categories != null) {
                    Category category = categories.get(position);
                    Log.d(TAG, "CategoryViewHolder: Click vào category - " + category.getName());
                    
                    // Cập nhật selected position
                    setSelectedPosition(position);
                    
                    // Gọi listener
                    clickListener.onCategoryClick(category);
                }
            });
        }
        
        /**
         * Bind dữ liệu category vào view
         * @param category Category cần bind
         * @param isSelected Category có được chọn không
         */
        public void bind(Category category, boolean isSelected) {
            Log.d(TAG, "CategoryViewHolder.bind: Bind category - " + category.getName() + 
                ", selected: " + isSelected);
            
            try {
                // Hiển thị tên category
                if (tvCategoryName != null) {
                    tvCategoryName.setText(category.getName());
                }
                
                // Hiển thị icon (sử dụng text icon đơn giản)
                if (tvCategoryIcon != null) {
                    String iconText = getIconText(category.getIconName());
                    tvCategoryIcon.setText(iconText);
                }
                
                // Thiết lập màu sắc và trạng thái selected
                if (cardView != null) {
                    try {
                        // Parse màu từ hex string
                        int color = Color.parseColor(category.getColor());
                        
                        if (isSelected) {
                            // Category được chọn - màu đậm
                            cardView.setCardBackgroundColor(color);
                            if (tvCategoryName != null) {
                                tvCategoryName.setTextColor(Color.WHITE);
                            }
                            if (tvCategoryIcon != null) {
                                tvCategoryIcon.setTextColor(Color.WHITE);
                            }
                        } else {
                            // Category không được chọn - màu nhạt
                            int lightColor = Color.argb(50, Color.red(color), Color.green(color), Color.blue(color));
                            cardView.setCardBackgroundColor(lightColor);
                            if (tvCategoryName != null) {
                                tvCategoryName.setTextColor(color);
                            }
                            if (tvCategoryIcon != null) {
                                tvCategoryIcon.setTextColor(color);
                            }
                        }
                        
                    } catch (IllegalArgumentException e) {
                        Log.e(TAG, "CategoryViewHolder.bind: Màu không hợp lệ - " + category.getColor(), e);
                        // Sử dụng màu mặc định
                        setDefaultColors(isSelected);
                    }
                }
                
                Log.d(TAG, "CategoryViewHolder.bind: Bind thành công");
                
            } catch (Exception e) {
                Log.e(TAG, "CategoryViewHolder.bind: Lỗi khi bind dữ liệu", e);
                setDefaultColors(isSelected);
            }
        }
        
        /**
         * Thiết lập màu mặc định
         * @param isSelected Category có được chọn không
         */
        private void setDefaultColors(boolean isSelected) {
            Log.d(TAG, "CategoryViewHolder.setDefaultColors: Thiết lập màu mặc định, selected: " + isSelected);
            
            if (cardView != null) {
                if (isSelected) {
                    cardView.setCardBackgroundColor(Color.parseColor("#2196F3")); // Blue
                    if (tvCategoryName != null) tvCategoryName.setTextColor(Color.WHITE);
                    if (tvCategoryIcon != null) tvCategoryIcon.setTextColor(Color.WHITE);
                } else {
                    cardView.setCardBackgroundColor(Color.parseColor("#E3F2FD")); // Light Blue
                    if (tvCategoryName != null) tvCategoryName.setTextColor(Color.parseColor("#2196F3"));
                    if (tvCategoryIcon != null) tvCategoryIcon.setTextColor(Color.parseColor("#2196F3"));
                }
            }
        }
        
        /**
         * Chuyển đổi tên icon thành text hiển thị
         * @param iconName Tên icon
         * @return Text hiển thị cho icon
         */
        private String getIconText(String iconName) {
            if (iconName == null) return "📁";
            
            // Mapping một số icon phổ biến thành emoji
            switch (iconName.toLowerCase()) {
                case "work":
                case "assignment":
                    return "💼";
                case "home":
                    return "🏠";
                case "school":
                case "book":
                    return "📚";
                case "fitness_center":
                case "sports":
                    return "💪";
                case "shopping_cart":
                case "store":
                    return "🛒";
                case "restaurant":
                case "local_cafe":
                    return "🍽️";
                case "flight":
                case "hotel":
                    return "✈️";
                case "favorite":
                case "star":
                    return "⭐";
                case "category":
                    return "📂";
                case "label":
                    return "🏷️";
                default:
                    return "📁";
            }
        }
    }
}
