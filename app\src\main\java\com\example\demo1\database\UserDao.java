package com.example.demo1.database;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.demo1.models.User;

import java.util.List;

/**
 * DAO (Data Access Object) cho User Entity
 * Chứa các phương thức để thao tác với bảng users trong database
 */
@Dao
public interface UserDao {
    
    /**
     * Thêm user mới vào database
     * @param user User cần thêm
     * @return ID của user vừa được thêm
     */
    @Insert
    long insertUser(User user);
    
    /**
     * Cập nhật thông tin user
     * @param user User cần cập nhật
     * @return Số dòng bị ảnh hưởng
     */
    @Update
    int updateUser(User user);
    
    /**
     * Xóa user khỏi database
     * @param user User cần xóa
     * @return Số dòng bị ảnh hưởng
     */
    @Delete
    int deleteUser(User user);
    
    /**
     * L<PERSON>y tất cả user đang hoạt động
     * @return LiveData chứa danh sách user
     */
    @Query("SELECT * FROM users WHERE is_active = 1 ORDER BY created_at DESC")
    LiveData<List<User>> getAllActiveUsers();
    
    /**
     * Lấy user theo ID
     * @param userId ID của user
     * @return LiveData chứa user
     */
    @Query("SELECT * FROM users WHERE user_id = :userId")
    LiveData<User> getUserById(int userId);
    
    /**
     * Lấy user theo username
     * @param username Username cần tìm
     * @return User tìm được (có thể null)
     */
    @Query("SELECT * FROM users WHERE username = :username LIMIT 1")
    User getUserByUsername(String username);
    
    /**
     * Lấy user theo email
     * @param email Email cần tìm
     * @return User tìm được (có thể null)
     */
    @Query("SELECT * FROM users WHERE email = :email LIMIT 1")
    User getUserByEmail(String email);
    
    /**
     * Kiểm tra xem username đã tồn tại chưa
     * @param username Username cần kiểm tra
     * @return Số lượng user có username này
     */
    @Query("SELECT COUNT(*) FROM users WHERE username = :username")
    int checkUsernameExists(String username);
    
    /**
     * Kiểm tra xem email đã tồn tại chưa
     * @param email Email cần kiểm tra
     * @return Số lượng user có email này
     */
    @Query("SELECT COUNT(*) FROM users WHERE email = :email")
    int checkEmailExists(String email);
    
    /**
     * Vô hiệu hóa user (soft delete)
     * @param userId ID của user cần vô hiệu hóa
     * @return Số dòng bị ảnh hưởng
     */
    @Query("UPDATE users SET is_active = 0 WHERE user_id = :userId")
    int deactivateUser(int userId);
    
    /**
     * Kích hoạt lại user
     * @param userId ID của user cần kích hoạt
     * @return Số dòng bị ảnh hưởng
     */
    @Query("UPDATE users SET is_active = 1 WHERE user_id = :userId")
    int activateUser(int userId);
    
    /**
     * Đếm tổng số user đang hoạt động
     * @return Số lượng user đang hoạt động
     */
    @Query("SELECT COUNT(*) FROM users WHERE is_active = 1")
    LiveData<Integer> getActiveUserCount();
}
